<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Panda · React Developer</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@400;600;700&family=Fira+Code:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="./styles.css" />
  <meta name="description" content="Portfolio of a funky Panda who builds delightful React apps." />
</head>
<body>
  <!-- Background decoration: floating bamboo and gradient blobs -->
  <div class="bg">
    <div class="blob blob-1"></div>
    <div class="blob blob-2"></div>
    <div class="blob blob-3"></div>

    <div class="bamboo bamboo-1"></div>
    <div class="bamboo bamboo-2"></div>
    <div class="bamboo bamboo-3"></div>
    <div class="bamboo bamboo-4"></div>
  </div>

  <header class="nav">
    <div class="logo">
      <span class="panda-face" aria-hidden="true">🐼</span>
      <span>PandaDev</span>
    </div>
    <nav>
      <a href="#about">About</a>
      <a href="#work">Projects</a>
      <a href="#skills">Skills</a>
      <a href="#contact" class="cta">Contact</a>
    </nav>
  </header>

  <main>
    <!-- Hero with parallax panda and typewriter -->
    <section class="hero">
      <div class="hero-inner">
        <div class="panda-hero" aria-hidden="true">
          <div class="panda-head">
            <div class="panda-ear left"></div>
            <div class="panda-ear right"></div>
            <div class="panda-eye left">
              <div class="pupil"></div>
            </div>
            <div class="panda-eye right">
              <div class="pupil"></div>
            </div>
            <div class="panda-nose"></div>
            <div class="panda-mouth"></div>
          </div>
        </div>
        <div class="hero-copy">
          <h1 class="typewriter">
            Hi, I’m Panda.
            <span data-rotate='["I build React apps.","I eat bamboo.","I ship delightful UIs.","I test with Jest.","I hook useEffect wisely."]'></span>
          </h1>
          <p class="subtitle">A funky frontend panda crafting playful experiences with React, TypeScript, and vibes.</p>
          <div class="hero-actions">
            <a href="#work" class="btn primary">See Projects</a>
            <a href="#contact" class="btn ghost">Hire Me</a>
          </div>
        </div>
      </div>
      <div class="scroll-indicator" aria-hidden="true">
        <span>Scroll</span>
        <div class="mouse">
          <div class="wheel"></div>
        </div>
      </div>
    </section>

    <!-- About -->
    <section id="about" class="section reveal">
      <div class="container">
        <h2>About</h2>
        <p>
          I’m a React-loving panda with a passion for clean code, smooth animations, and snacks. I turn user needs into slick components, reusable hooks, and friendly interfaces. When I’m not building, I’m probably optimizing bundle size or doing bamboo-fueled code reviews.
        </p>
        <ul class="highlights">
          <li>Component-driven development with Storybook mindset</li>
          <li>State management with Redux Toolkit / Zustand</li>
          <li>Performance: memoization, suspense, lazy routes, code-splitting</li>
          <li>Testing: Jest + React Testing Library</li>
          <li>Styling: CSS Modules, Tailwind, or styled-components (CSS enjoyer first)</li>
        </ul>
      </div>
    </section>

    <!-- Work / Projects -->
    <section id="work" class="section alt reveal">
      <div class="container">
        <h2>Selected Projects</h2>
        <div class="grid projects">
          <article class="card project tilt">
            <div class="card-header">
              <div class="pill">React</div>
              <div class="pill green">TypeScript</div>
            </div>
            <h3>Bamboo Board</h3>
            <p>A Trello-esque kanban built with React + Zustand. Smooth DnD, offline-first, and gorgeous micro-interactions.</p>
            <div class="links">
              <a href="#" class="chip">Live Demo</a>
              <a href="#" class="chip">GitHub</a>
            </div>
          </article>

          <article class="card project tilt">
            <div class="card-header">
              <div class="pill purple">Next.js</div>
              <div class="pill">SSR</div>
            </div>
            <h3>Panda Recipes</h3>
            <p>Server-rendered recipe explorer with search, filters, and image optimization. Tastes like victory and bamboo.</p>
            <div class="links">
              <a href="#" class="chip">Live Demo</a>
              <a href="#" class="chip">GitHub</a>
            </div>
          </article>

          <article class="card project tilt">
            <div class="card-header">
              <div class="pill orange">Animations</div>
              <div class="pill">CSS</div>
            </div>
            <h3>React Motion Zoo</h3>
            <p>A collection of reusable animation patterns for React components using pure CSS and requestAnimationFrame.</p>
            <div class="links">
              <a href="#" class="chip">Live Demo</a>
              <a href="#" class="chip">GitHub</a>
            </div>
          </article>
        </div>
      </div>
    </section>

    <!-- Skills -->
    <section id="skills" class="section reveal">
      <div class="container">
        <h2>Skills</h2>
        <div class="skills">
          <div class="skill">
            <span>React</span>
            <div class="bar"><div class="fill" style="--pct: 95%"></div></div>
          </div>
          <div class="skill">
            <span>TypeScript</span>
            <div class="bar"><div class="fill" style="--pct: 88%"></div></div>
          </div>
          <div class="skill">
            <span>CSS</span>
            <div class="bar"><div class="fill" style="--pct: 92%"></div></div>
          </div>
          <div class="skill">
            <span>Testing</span>
            <div class="bar"><div class="fill" style="--pct: 80%"></div></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact -->
    <section id="contact" class="section alt reveal">
      <div class="container">
        <h2>Contact</h2>
        <p>Want to build something delightful? Ping me. I reply between bites.</p>
        <form class="contact-form" onsubmit="return false;">
          <div class="row">
            <input type="text" placeholder="Your Name" required />
            <input type="email" placeholder="Your Email" required />
          </div>
          <textarea placeholder="Your Message" rows="5" required></textarea>
          <button class="btn primary wobble" id="sendBtn" type="submit">Send Message</button>
          <p class="form-status" id="formStatus" role="status" aria-live="polite"></p>
        </form>
      </div>
    </section>
  </main>

  <footer class="footer">
    <div class="container">
      <p>© <span id="y"></span> Panda · React Developer · Built with HTML, CSS, JS</p>
      <div class="foot-icons">
        <a class="chip" href="#" title="GitHub">GitHub</a>
        <a class="chip" href="#" title="LinkedIn">LinkedIn</a>
        <a class="chip" href="#" title="X/Twitter">X</a>
      </div>
    </div>
  </footer>

  <script src="./script.js"></script>
</body>
</html>
