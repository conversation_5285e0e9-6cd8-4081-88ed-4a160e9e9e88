/* Parallax tilt for project cards */
(function(){
  const maxTilt = 12; // deg
  const cards = document.querySelectorAll('.tilt');
  cards.forEach(card => {
    card.addEventListener('mousemove', (e) => {
      const r = card.getBoundingClientRect();
      const px = (e.clientX - r.left) / r.width;
      const py = (e.clientY - r.top) / r.height;
      const rx = (py - 0.5) * -2 * maxTilt;
      const ry = (px - 0.5) * 2 * maxTilt;
      card.style.transform = `rotateX(${rx.toFixed(2)}deg) rotateY(${ry.toFixed(2)}deg) translateY(-6px)`;
    });
    card.addEventListener('mouseleave', () => {
      card.style.transform = '';
    });
  });
})();

/* Typewriter rotating phrases */
(function(){
  const el = document.querySelector('.typewriter span');
  if(!el) return;
  const phrases = JSON.parse(el.getAttribute('data-rotate') || '[]');
  let idx = 0;
  let char = 0;
  let typing = true;

  function tick(){
    const current = phrases[idx % phrases.length] || '';
    if(typing){
      char++;
      el.textContent = current.slice(0, char);
      if(char >= current.length){
        typing = false;
        setTimeout(tick, 1200);
        return;
      }
      setTimeout(tick, 40 + Math.random()*60);
    }else{
      // deleting
      char--;
      el.textContent = current.slice(0, char);
      if(char <= 0){
        typing = true;
        idx++;
        setTimeout(tick, 250);
        return;
      }
      setTimeout(tick, 30 + Math.random()*40);
    }
  }
  tick();
})();

/* Reveal on scroll */
(function(){
  const els = document.querySelectorAll('.reveal');
  const io = new IntersectionObserver((entries) => {
    entries.forEach(e => {
      if(e.isIntersecting){
        e.target.classList.add('visible');
        io.unobserve(e.target);
      }
    });
  }, { threshold: 0.12 });
  els.forEach(el => io.observe(el));
})();

/* Parallax panda head follow mouse */
(function(){
  const head = document.querySelector('.panda-head');
  const pupils = document.querySelectorAll('.pupil');
  if(!head) return;
  const strength = 8;

  window.addEventListener('mousemove', (e) => {
    const r = head.getBoundingClientRect();
    const cx = r.left + r.width/2;
    const cy = r.top + r.height/2;
    const dx = (e.clientX - cx) / (r.width/2);
    const dy = (e.clientY - cy) / (r.height/2);
    head.style.transform = `translateY(-2px) rotateX(${dy*-4}deg) rotateY(${dx*4}deg)`;

    pupils.forEach(p => {
      const moveX = Math.max(-1, Math.min(1, dx)) * strength;
      const moveY = Math.max(-1, Math.min(1, dy)) * strength;
      p.style.transform = `translate(${moveX}px, ${moveY}px)`;
    });
  });
})();

/* Simple form feedback + fun wobble */
(function(){
  const form = document.querySelector('.contact-form');
  const status = document.getElementById('formStatus');
  const sendBtn = document.getElementById('sendBtn');
  if(!form || !status || !sendBtn) return;

  form.addEventListener('submit', () => {
    sendBtn.disabled = true;
    status.textContent = 'Sending...';
    setTimeout(() => {
      status.textContent = 'Thanks! Your message has been delivered via carrier bamboo 🐼🎋';
      sendBtn.disabled = false;
      form.reset();
    }, 900);
  });
})();

/* Footer year */
(function(){
  const y = document.getElementById('y');
  if(y) y.textContent = new Date().getFullYear();
})();
