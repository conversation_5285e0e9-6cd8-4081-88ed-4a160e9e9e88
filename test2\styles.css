:root{
  --bg:#0d0f14;
  --surface:#121622;
  --surface-2:#0f1420;
  --text:#eaf2ff;
  --muted:#9fb3d9;
  --accent:#54e38e;
  --accent-2:#66d9ff;
  --accent-3:#f39cff;
  --warning:#ffd166;
  --shadow: 0 10px 30px rgba(0,0,0,.35);
}

*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  font-family:'Fredoka', system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  color:var(--text);
  background:
    radial-gradient(1200px 600px at 10% -10%, #1b2338 0%, transparent 60%),
    radial-gradient(900px 500px at 110% -20%, #18243a 0%, transparent 60%),
    var(--bg);
  overflow-x:hidden;
}

a{color:inherit;text-decoration:none}

/* Background blobs */
.bg{
  position:fixed; inset:0; pointer-events:none; z-index:-1;
}
.blob{
  position:absolute; filter: blur(40px); opacity:0.35; mix-blend-mode:screen;
  animation: float 18s ease-in-out infinite;
}
.blob-1{ width:420px; height:420px; left:-120px; top:-120px; background: radial-gradient(circle at 30% 30%, #3ae0a1, #00b37a);}
.blob-2{ width:520px; height:520px; right:-160px; top:-160px; background: radial-gradient(circle at 60% 40%, #64d3ff, #2c7bff); animation-delay: -6s;}
.blob-3{ width:460px; height:460px; left:40%; bottom:-200px; background: radial-gradient(circle at 50% 50%, #ff9cf2, #c560f5); animation-delay: -12s;}

@keyframes float{
  0%,100%{transform: translateY(0) translateX(0) scale(1)}
  50%{transform: translateY(-25px) translateX(10px) scale(1.05)}
}

/* Floating bamboo */
.bamboo{
  position:absolute; width:10px; height:180px; background: linear-gradient(#2b5c3e,#447a55);
  border-radius:6px; opacity:.25; box-shadow: inset 0 0 0 2px rgba(255,255,255,.08);
  animation: sway 7s ease-in-out infinite;
}
.bamboo:before,.bamboo:after{
  content:""; position:absolute; left:0; right:0; height:4px; background:rgba(255,255,255,.15); border-radius:4px;
}
.bamboo:before{ top:40px}
.bamboo:after{ top:100px}
.bamboo-1{ left:6%; top:18%}
.bamboo-2{ left:82%; top:26%; animation-delay:-2s}
.bamboo-3{ left:12%; top:70%; height:220px; animation-delay:-4s}
.bamboo-4{ left:90%; top:60%; height:150px; animation-delay:-6s}

@keyframes sway{
  0%,100%{ transform: rotate(-2deg)}
  50%{ transform: rotate(2deg)}
}

/* Navigation */
.nav{
  position:sticky; top:0; z-index:50; backdrop-filter: blur(10px);
  background: linear-gradient(to bottom, rgba(10,12,18,.65), rgba(10,12,18,0));
  border-bottom: 1px solid rgba(255,255,255,.06);
  display:flex; align-items:center; justify-content:space-between; padding:14px 24px;
}
.logo{ display:flex; align-items:center; gap:10px; font-weight:700; letter-spacing:.3px}
.panda-face{ filter: drop-shadow(0 2px 6px rgba(0,0,0,.4)); font-size:22px}
.nav nav{ display:flex; gap:18px; align-items:center}
.nav a{ color:var(--muted); font-weight:600; padding:8px 10px; border-radius:8px; transition:.2s transform, .2s background}
.nav a:hover{ color:var(--text); background: rgba(255,255,255,.06); transform: translateY(-1px)}
.nav a.cta{ background: linear-gradient(135deg, var(--accent), #2bd4a6); color:#0b1720; box-shadow: 0 6px 18px rgba(43,212,166,.35); }
.nav a.cta:hover{ transform: translateY(-1px) scale(1.02)}

/* Hero */
.hero{
  min-height:80vh; display:flex; align-items:center; position:relative;
  padding:60px 24px 100px;
}
.hero-inner{
  width:min(1100px, 92vw); margin:0 auto; display:grid; grid-template-columns: 1.1fr 1fr; gap:42px; align-items:center;
}
.panda-hero{ position:relative; height:360px; perspective: 800px; }
.panda-head{
  width:320px; height:320px; background: radial-gradient(circle at 50% 35%, #fff 0%, #e7ebff 70%);
  border-radius:50%; margin:auto; position:relative; box-shadow: var(--shadow);
  transform-style: preserve-3d; animation: headbob 5s ease-in-out infinite;
}
@keyframes headbob{
  0%,100%{ transform: translateY(0) rotateX(0deg) rotateY(0deg)}
  50%{ transform: translateY(-8px) rotateX(4deg) rotateY(-3deg)}
}
.panda-ear{
  width:110px; height:110px; background: radial-gradient(#1b1d25, #0e1118);
  border-radius:50%; position:absolute; top:-28px; box-shadow: inset 0 -10px 25px rgba(0,0,0,.5);
}
.panda-ear.left{ left:-6px; transform: rotate(-8deg) }
.panda-ear.right{ right:-6px; transform: rotate(8deg) }

.panda-eye{
  width:110px; height:120px; background: radial-gradient(ellipse at 60% 40%, #1e2230, #0c0f16);
  border-radius:60% 40% 50% 50% / 60% 40% 50% 50%;
  position:absolute; top:90px; box-shadow: inset 0 0 0 2px rgba(255,255,255,.06);
}
.panda-eye.left{ left:55px; transform: rotate(-12deg) }
.panda-eye.right{ right:55px; transform: rotate(12deg) }
.pupil{
  width:28px; height:28px; background:#000; border-radius:50%; position:absolute; top:44px; left:44px;
  box-shadow: 0 0 0 6px #0a0c11, 0 0 0 10px rgba(255,255,255,.06);
}
.pupil:after{
  content:""; position:absolute; width:10px; height:10px; background:#fff; border-radius:50%; top:4px; left:6px; opacity:.8
}
.panda-nose{
  width:34px; height:28px; background:#0c0f16; border-radius: 60% 60% 70% 70%;
  position:absolute; top:160px; left:50%; transform: translateX(-50%);
  box-shadow: inset 0 -3px 6px rgba(255,255,255,.08)
}
.panda-mouth{
  width:60px; height:24px; border:3px solid #0c0f16; border-top:none; border-radius:0 0 60px 60px;
  position:absolute; top:190px; left:50%; transform: translateX(-50%);
}

.hero-copy h1{
  font-size: clamp(32px, 4vw, 48px);
  line-height:1.12; margin:0 0 8px;
}
.subtitle{ color:var(--muted); margin:0 0 24px; font-weight:500}
.hero-actions{ display:flex; gap:14px; flex-wrap:wrap}

.btn{
  display:inline-flex; align-items:center; justify-content:center; gap:8px;
  padding:12px 18px; border-radius:12px; font-weight:700; letter-spacing:.3px; transition:.2s transform, .2s box-shadow, .2s background;
}
.btn.primary{ background: linear-gradient(135deg, var(--accent), #2bd4a6); color:#07221a; box-shadow: 0 10px 24px rgba(43,212,166,.35)}
.btn.primary:hover{ transform: translateY(-2px) scale(1.02)}
.btn.ghost{ background: rgba(255,255,255,.06); border:1px solid rgba(255,255,255,.12)}
.btn.ghost:hover{ background: rgba(255,255,255,.12); transform: translateY(-2px)}

/* Scroll Indicator */
.scroll-indicator{
  position:absolute; left:50%; bottom:14px; transform: translateX(-50%); color:var(--muted);
  display:flex; flex-direction:column; gap:8px; align-items:center; font-size:12px; letter-spacing:.2px
}
.mouse{ width:26px; height:40px; border:2px solid rgba(255,255,255,.35); border-radius:16px; position:relative}
.wheel{ width:4px; height:8px; background:rgba(255,255,255,.55); border-radius:4px; position:absolute; left:50%; top:8px; transform: translateX(-50%); animation: wheel 1.8s ease-in-out infinite}
@keyframes wheel{
  0%{ opacity:0; transform: translate(-50%, 0)}
  30%{ opacity:1}
  100%{ opacity:0; transform: translate(-50%, 12px)}
}

/* Sections */
.section{ padding:86px 24px}
.section.alt{ background: linear-gradient(180deg, rgba(255,255,255,.03), rgba(255,255,255,0))}
.container{ width:min(1100px, 92vw); margin:0 auto}
h2{ margin:0 0 16px; font-size: clamp(24px, 3vw, 32px) }
p{ line-height:1.6}

.highlights{
  margin:18px 0 0; padding:0; list-style:none; display:grid; gap:10px;
}
.highlights li{
  color:var(--muted); background: rgba(255,255,255,.04); border:1px solid rgba(255,255,255,.06);
  padding:10px 12px; border-radius:10px;
}

/* Projects */
.grid.projects{
  display:grid; grid-template-columns: repeat(3, minmax(0,1fr)); gap:18px;
}
.card{
  background: linear-gradient(180deg, rgba(20,24,38,.9), rgba(14,18,28,.9));
  border:1px solid rgba(255,255,255,.08); border-radius:16px; padding:16px; box-shadow: var(--shadow);
  transform: translateY(0); transition: .25s transform, .25s box-shadow, .25s border;
}
.card:hover{ transform: translateY(-6px); box-shadow: 0 18px 40px rgba(0,0,0,.45); border-color: rgba(255,255,255,.14)}
.card-header{ display:flex; gap:8px; margin-bottom:10px}
.pill{
  font-family:'Fira Code', ui-monospace, monospace; font-size:12px; color:#0a1320;
  background: linear-gradient(135deg, var(--accent-2), #53a8ff); padding:6px 8px; border-radius:999px;
}
.pill.green{ background: linear-gradient(135deg, var(--accent), #2bd4a6)}
.pill.purple{ background: linear-gradient(135deg, #c560f5, #ff9cf2)}
.pill.orange{ background: linear-gradient(135deg, #ffb36c, #ff7e47)}
.card h3{ margin:6px 0 6px}
.card p{ color:var(--muted); margin:0 0 12px}
.links{ display:flex; gap:10px; flex-wrap:wrap}
.chip{
  font-family:'Fira Code', ui-monospace, monospace; font-size:12px; color:var(--text);
  padding:8px 10px; border-radius:999px; background: rgba(255,255,255,.06); border:1px solid rgba(255,255,255,.12);
  transition:.2s transform, .2s background;
}
.chip:hover{ background: rgba(255,255,255,.12); transform: translateY(-1px)}

/* Tilt effect hint */
.tilt{ will-change: transform; transform-style: preserve-3d}

/* Skills */
.skills{ display:grid; gap:14px}
.skill span{ display:block; margin-bottom:6px; font-weight:600}
.bar{
  height:12px; background: rgba(255,255,255,.06); border:1px solid rgba(255,255,255,.08);
  border-radius:999px; overflow:hidden;
}
.fill{
  --pct: 50%;
  height:100%; width: var(--pct);
  background: linear-gradient(90deg, var(--accent), var(--accent-2) 60%, var(--accent-3));
  box-shadow: inset 0 0 10px rgba(0,0,0,.3);
  animation: grow 1.2s ease-out both;
}
@keyframes grow{
  from{ width:0 }
  to{ width: var(--pct)}
}

/* Contact */
.contact-form{
  margin-top:12px; display:flex; flex-direction:column; gap:12px;
}
.row{ display:grid; grid-template-columns: 1fr 1fr; gap:12px}
input, textarea{
  width:100%; padding:12px 14px; border-radius:12px; color:var(--text);
  background: rgba(255,255,255,.06); border:1px solid rgba(255,255,255,.12);
  outline:none; transition:.2s border, .2s background;
  font-family: inherit;
}
input:focus, textarea:focus{ border-color: var(--accent-2); background: rgba(255,255,255,.1)}
.form-status{ min-height:22px; color:var(--warning); margin:0}

/* Footer */
.footer{
  padding:28px 24px; border-top:1px solid rgba(255,255,255,.06);
  background: linear-gradient(180deg, rgba(255,255,255,.02), rgba(255,255,255,.04));
}
.footer .container{ display:flex; align-items:center; justify-content:space-between; gap:16px; flex-wrap:wrap}
.foot-icons{ display:flex; gap:8px}

/* Typewriter rotating text */
.typewriter span{
  display:inline-block; min-width: 12ch; color: var(--accent);
  position:relative;
}
.typewriter span:after{
  content:""; position:absolute; right:-4px; top:50%; width:2px; height:1.2em; background: var(--accent);
  transform: translateY(-50%); animation: caret 1.2s steps(1) infinite;
}
@keyframes caret{ 0%,50%{opacity:1} 50.01%,100%{opacity:0} }

/* Wobble button hover */
.wobble{ will-change: transform}
.wobble:hover{ animation: wobble 0.7s both}
@keyframes wobble{
  0%{transform: translateZ(0)}
  15%{transform: rotate(-3deg) scale(1.02)}
  30%{transform: rotate(3deg)}
  45%{transform: rotate(-2deg)}
  60%{transform: rotate(2deg)}
  100%{transform: rotate(0) scale(1.01)}
}

/* Reveal on scroll */
.reveal{ opacity:0; transform: translateY(16px); transition: .6s opacity ease, .6s transform ease}
.reveal.visible{ opacity:1; transform: translateY(0)}

/* Responsive */
@media (max-width: 900px){
  .hero-inner{ grid-template-columns: 1fr; gap:22px}
  .panda-hero{ order:-1; height:300px}
  .panda-head{ width:260px; height:260px}
  .grid.projects{ grid-template-columns: 1fr}
  .row{ grid-template-columns: 1fr}
}
