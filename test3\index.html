<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Panda.dev — React Developer</title>
  <meta name="description" content="Panda is a funky React Developer building delightful UIs with bamboo-powered performance." />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link href="https://fonts.googleapis.com/css2?family=Urbanist:wght@400;600;800;900&family=Fira+Code:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="./styles.css" />
</head>
<body>
  <canvas id="bg"></canvas>

  <header class="site-header">
    <div class="brand">
      <div class="panda-logo" aria-hidden="true">
        <div class="ear left"></div>
        <div class="ear right"></div>
        <div class="face">
          <div class="eye left"></div>
          <div class="eye right"></div>
          <div class="nose"></div>
        </div>
      </div>
      <div class="title-wrap">
        <h1 class="site-title"><span class="neon">Panda</span>.dev</h1>
        <p class="tagline">Funky React Developer • Animations • Delightful UX</p>
      </div>
    </div>
    <nav class="nav">
      <button class="theme-toggle" id="themeToggle" title="Toggle theme" aria-label="Toggle theme">
        <span class="sun">☀️</span><span class="moon">🌙</span>
      </button>
      <a href="#about">About</a>
      <a href="#projects">Projects</a>
      <a href="#skills">Skills</a>
      <a href="#contact">Contact</a>
    </nav>
  </header>

  <main>
    <section class="hero">
      <div class="hero-content">
        <h2 class="headline">Building playful, performant UIs with React</h2>
        <p class="subhead">
          I’m a bamboo-fueled panda crafting snappy interfaces, smooth animations, and component systems
          your users will love. Let’s make your product delightfully sticky.
        </p>
        <div class="cta-row">
          <a class="btn primary" href="#projects">See Projects</a>
          <a class="btn ghost" href="#contact">Hire Panda</a>
        </div>
      </div>
      <div class="hero-badges">
        <span class="badge">React</span>
        <span class="badge">TypeScript</span>
        <span class="badge">Next.js</span>
        <span class="badge">Vite</span>
        <span class="badge">Framer Motion</span>
      </div>
    </section>

    <section id="about" class="panel">
      <div class="panel-content">
        <h3 class="section-title">About The Panda</h3>
        <p class="lead">
          Hello, I’m Panda — a front-end engineer specialized in React and motion design.
          I turn brittle UI into bamboo-strong component architectures, with DX tooling that purrs.
        </p>
        <ul class="about-list">
          <li>Component libraries with accessibility baked-in</li>
          <li>State management that doesn’t make you hiss — Redux, Zustand, or signals</li>
          <li>Animation systems with CSS and JS, tuned for 60fps</li>
          <li>SSR, SSG, RSC — modern rendering done right</li>
        </ul>
      </div>
    </section>

    <section id="projects" class="panel">
      <div class="panel-content">
        <h3 class="section-title">Featured Projects</h3>
        <div class="cards">
          <article class="card" data-tilt>
            <div class="card-top">
              <div class="react-orb" aria-hidden="true"></div>
            </div>
            <div class="card-body">
              <h4>Component Galaxy</h4>
              <p>Design system of 60+ accessible React components with tokens and theming.</p>
              <div class="chips">
                <span>React</span><span>TypeScript</span><span>Storybook</span>
              </div>
              <div class="actions">
                <a href="#" class="text-link">Live Demo</a>
                <a href="#" class="text-link">GitHub</a>
              </div>
            </div>
          </article>

          <article class="card" data-tilt>
            <div class="card-top">
              <div class="react-orb teal" aria-hidden="true"></div>
            </div>
            <div class="card-body">
              <h4>Motion Playground</h4>
              <p>Micro-interactions and page transitions for a content platform.</p>
              <div class="chips">
                <span>Next.js</span><span>Framer</span><span>CSS</span>
              </div>
              <div class="actions">
                <a href="#" class="text-link">Case Study</a>
                <a href="#" class="text-link">GitHub</a>
              </div>
            </div>
          </article>

          <article class="card" data-tilt>
            <div class="card-top">
              <div class="react-orb violet" aria-hidden="true"></div>
            </div>
            <div class="card-body">
              <h4>Bamboo Board</h4>
              <p>Kanban app with optimistic updates and offline-first patterns.</p>
              <div class="chips">
                <span>React</span><span>Zustand</span><span>PWA</span>
              </div>
              <div class="actions">
                <a href="#" class="text-link">Live Demo</a>
                <a href="#" class="text-link">GitHub</a>
              </div>
            </div>
          </article>
        </div>
      </div>
    </section>

    <section id="skills" class="panel compact">
      <h3 class="section-title">Skills</h3>
      <div class="marquee" aria-label="Skill badges">
        <div class="track">
          <span class="pill">React</span>
          <span class="pill">TypeScript</span>
          <span class="pill">Next.js</span>
          <span class="pill">Redux</span>
          <span class="pill">Zustand</span>
          <span class="pill">Node</span>
          <span class="pill">Vite</span>
          <span class="pill">Jest</span>
          <span class="pill">Playwright</span>
          <span class="pill">Tailwind</span>
          <span class="pill">CSS</span>
          <span class="pill">RSC</span>
          <span class="pill">A11y</span>
          <span class="pill">Perf</span>
          <span class="pill">Animations</span>
          <!-- duplicate for seamless loop -->
          <span class="pill">React</span>
          <span class="pill">TypeScript</span>
          <span class="pill">Next.js</span>
          <span class="pill">Redux</span>
          <span class="pill">Zustand</span>
          <span class="pill">Node</span>
          <span class="pill">Vite</span>
          <span class="pill">Jest</span>
          <span class="pill">Playwright</span>
          <span class="pill">Tailwind</span>
          <span class="pill">CSS</span>
          <span class="pill">RSC</span>
          <span class="pill">A11y</span>
          <span class="pill">Perf</span>
          <span class="pill">Animations</span>
        </div>
      </div>
    </section>

    <section id="contact" class="panel">
      <div class="panel-content">
        <h3 class="section-title">Let’s Build Something Delightful</h3>
        <p class="lead">Looking for a React specialist who ships fast and fun? I’m your panda.</p>
        <div class="contact-cta">
          <a class="btn goo" href="mailto:<EMAIL>?subject=Let’s%20build%20with%20React">
            <span class="shine"></span>
            Contact Panda
          </a>
          <div class="socials">
            <a href="#" aria-label="GitHub" class="social">GH</a>
            <a href="#" aria-label="Twitter" class="social">X</a>
            <a href="#" aria-label="LinkedIn" class="social">in</a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <p>© <span id="year"></span> Panda.dev — crafted with bamboo and React vibes.</p>
  </footer>

  <script src="./script.js"></script>
</body>
</html>
