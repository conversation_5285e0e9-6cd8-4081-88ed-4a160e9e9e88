// Panda Portfolio Interactions
(() => {
  const root = document.documentElement;
  const themeToggle = document.getElementById('themeToggle');
  const yearSpan = document.getElementById('year');
  if (yearSpan) yearSpan.textContent = new Date().getFullYear().toString();

  // Theme: load preference
  const KEY = 'panda-theme';
  const prefersLight = window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches;
  const saved = localStorage.getItem(KEY);
  const initialLight = saved ? saved === 'light' : prefersLight;
  if (initialLight) root.classList.add('light');

  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      root.classList.toggle('light');
      localStorage.setItem(KEY, root.classList.contains('light') ? 'light' : 'dark');
    });
  }

  // Scroll reveal
  const revealEls = Array.from(document.querySelectorAll('.panel, .card, .hero-content, .hero-badges'));
  revealEls.forEach(el => el.classList.add('reveal'));
  const io = new IntersectionObserver((entries) => {
    for (const e of entries) {
      if (e.isIntersecting) {
        e.target.classList.add('visible');
        io.unobserve(e.target);
      }
    }
  }, { rootMargin: '0px 0px -10% 0px', threshold: 0.1 });
  revealEls.forEach(el => io.observe(el));

  // Card tilt interaction
  const tiltCards = Array.from(document.querySelectorAll('[data-tilt]'));
  tiltCards.forEach(card => {
    const dampen = 12; // smaller = more tilt
    let raf = null;

    function onMove(e) {
      const rect = card.getBoundingClientRect();
      const cx = rect.left + rect.width / 2;
      const cy = rect.top + rect.height / 2;
      const dx = (e.clientX - cx) / rect.width;
      const dy = (e.clientY - cy) / rect.height;
      const rx = (dy * 2) * dampen; // rotateX
      const ry = (-dx * 2) * dampen; // rotateY

      if (raf) cancelAnimationFrame(raf);
      raf = requestAnimationFrame(() => {
        card.style.transform = `perspective(800px) rotateX(${rx}deg) rotateY(${ry}deg) translateZ(0)`;
        card.style.boxShadow = `0 18px 40px rgba(0,0,0,0.35)`;
      });
    }

    function onLeave() {
      if (raf) cancelAnimationFrame(raf);
      raf = requestAnimationFrame(() => {
        card.style.transform = `perspective(800px) rotateX(0deg) rotateY(0deg) translateZ(0)`;
        card.style.boxShadow = '';
      });
    }

    card.addEventListener('mousemove', onMove);
    card.addEventListener('mouseleave', onLeave);
    card.addEventListener('touchmove', (e) => {
      const t = e.touches[0];
      if (!t) return;
      onMove(t);
    }, { passive: true });
    card.addEventListener('touchend', onLeave);
  });

  // Parallax particle canvas background
  const canvas = document.getElementById('bg');
  if (canvas) {
    const ctx = canvas.getContext('2d', { alpha: true });
    let w = canvas.width = window.innerWidth;
    let h = canvas.height = window.innerHeight;

    const DPR = Math.min(window.devicePixelRatio || 1, 2);
    canvas.width = w * DPR;
    canvas.height = h * DPR;
    ctx.scale(DPR, DPR);

    const particles = [];
    const count = Math.min(80, Math.floor((w * h) / 18000));
    const colors = [
      'rgba(102,243,243,0.6)',
      'rgba(169,107,255,0.55)',
      'rgba(255,107,214,0.45)'
    ];

    function spawnParticle() {
      return {
        x: Math.random() * w,
        y: Math.random() * h,
        r: Math.random() * 2 + 0.6,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        color: colors[(Math.random() * colors.length) | 0]
      };
    }

    for (let i = 0; i < count; i++) particles.push(spawnParticle());

    let mouse = { x: w / 2, y: h / 2 };
    window.addEventListener('mousemove', (e) => {
      mouse.x = e.clientX;
      mouse.y = e.clientY;
    });

    window.addEventListener('resize', () => {
      w = window.innerWidth;
      h = window.innerHeight;
      canvas.width = w * DPR;
      canvas.height = h * DPR;
      ctx.scale(DPR, DPR);
    });

    function draw() {
      ctx.clearRect(0, 0, w, h);

      // subtle gradient glow responding to mouse
      const gx = mouse.x, gy = mouse.y;
      const grad = ctx.createRadialGradient(gx, gy, 20, gx, gy, 220);
      grad.addColorStop(0, 'rgba(255,255,255,0.06)');
      grad.addColorStop(1, 'rgba(255,255,255,0.0)');
      ctx.fillStyle = grad;
      ctx.fillRect(0, 0, w, h);

      // particles
      particles.forEach(p => {
        p.x += p.vx;
        p.y += p.vy;

        // wrap around edges
        if (p.x < -10) p.x = w + 10;
        if (p.x > w + 10) p.x = -10;
        if (p.y < -10) p.y = h + 10;
        if (p.y > h + 10) p.y = -10;

        // parallax pull towards mouse slightly
        const dx = gx - p.x;
        const dy = gy - p.y;
        const dist = Math.hypot(dx, dy) || 1;
        const force = Math.min(0.02 / dist, 0.02);
        p.vx += dx * force * 0.02;
        p.vy += dy * force * 0.02;

        ctx.beginPath();
        ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2);
        ctx.fillStyle = p.color;
        ctx.fill();
      });

      // lines between close particles
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const a = particles[i], b = particles[j];
          const dx = a.x - b.x, dy = a.y - b.y;
          const d2 = dx * dx + dy * dy;
          if (d2 < 140 * 140) {
            const alpha = 1 - d2 / (140 * 140);
            ctx.strokeStyle = `rgba(150,170,255,${alpha * 0.15})`;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(a.x, a.y);
            ctx.lineTo(b.x, b.y);
            ctx.stroke();
          }
        }
      }

      requestAnimationFrame(draw);
    }
    draw();
  }

  // Smooth anchor nav (offset for sticky header)
  const header = document.querySelector('.site-header');
  document.querySelectorAll('a[href^="#"]').forEach(link => {
    link.addEventListener('click', (e) => {
      const id = link.getAttribute('href') || '';
      if (id.length > 1) {
        const el = document.querySelector(id);
        if (el) {
          e.preventDefault();
          const rect = el.getBoundingClientRect();
          const top = window.scrollY + rect.top - (header ? header.offsetHeight + 8 : 0);
          window.scrollTo({ top, behavior: 'smooth' });
        }
      }
    });
  });
})();
