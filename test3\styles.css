:root {
  --bg: #0a0a0f;
  --bg-2: #0f0f17;
  --text: #e7ecff;
  --muted: #9aa3b2;
  --neon: #66f3f3;
  --accent: #a96bff;
  --accent-2: #ff6bd6;
  --card: #121222;
  --surface: #0d0d18;
  --pill-bg: #17172a;
  --success: #24e88b;
  --warning: #ffd166;

  --shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.45);
  --shadow-md: 0 10px 30px rgba(0, 0, 0, 0.35);

  --radius: 16px;
  --radius-sm: 10px;
}

:root.light {
  --bg: #f7f8fe;
  --bg-2: #ffffff;
  --text: #09111f;
  --muted: #3f4a5f;
  --neon: #0fb9b1;
  --accent: #6a4df5;
  --accent-2: #ff2e93;
  --card: #ffffff;
  --surface: #f0f2ff;
  --pill-bg: #eef0ff;
  --success: #0ea765;
  --warning: #e19f00;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: "Urbanist", system-ui, -apple-system, Segoe UI, Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji","Segoe UI Emoji";
  color: var(--text);
  background: radial-gradient(1200px 800px at 20% -10%, rgba(102, 243, 243, 0.18), transparent 60%),
              radial-gradient(1000px 600px at 110% 10%, rgba(169, 107, 255, 0.18), transparent 60%),
              linear-gradient(180deg, var(--bg), var(--bg-2));
  overflow-x: hidden;
}

/* Canvas background */
#bg {
  position: fixed;
  inset: 0;
  z-index: -1;
}

/* Header */
.site-header {
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  padding: 16px 24px;
  background: linear-gradient(0deg, rgba(0,0,0,0), rgba(0,0,0,0.25));
  backdrop-filter: blur(8px);
}

.brand {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-wrap {
  line-height: 1;
}

.site-title {
  margin: 0;
  font-weight: 900;
  letter-spacing: 0.5px;
  font-size: clamp(20px, 2.6vw, 36px);
}

.site-title .neon {
  color: var(--neon);
  text-shadow: 0 0 18px color-mix(in oklab, var(--neon), transparent 60%);
}

.tagline {
  margin: 4px 0 0;
  font-size: 13px;
  color: var(--muted);
}

.nav {
  display: flex;
  align-items: center;
  gap: 14px;
}

.nav a {
  color: var(--text);
  text-decoration: none;
  font-weight: 600;
  opacity: 0.8;
}

.nav a:hover {
  opacity: 1;
  text-underline-offset: 4px;
  text-decoration: underline;
}

.theme-toggle {
  display: inline-flex;
  gap: 6px;
  align-items: center;
  justify-content: center;
  border: 1px solid color-mix(in oklab, var(--text), transparent 80%);
  background: color-mix(in oklab, var(--card), transparent 15%);
  color: var(--text);
  padding: 8px 10px;
  border-radius: 999px;
  cursor: pointer;
}

.theme-toggle .sun { display: none; }
:root.light .theme-toggle .sun { display: inline; }
:root.light .theme-toggle .moon { display: none; }

/* Panda logo */
.panda-logo {
  position: relative;
  width: 48px;
  height: 48px;
  filter: drop-shadow(0 4px 12px rgba(0,0,0,0.4));
  animation: float 5s ease-in-out infinite;
}
.panda-logo .ear {
  position: absolute;
  width: 16px;
  height: 16px;
  background: #000;
  border-radius: 50%;
  top: -2px;
}
.panda-logo .ear.left { left: 6px; }
.panda-logo .ear.right { right: 6px; }
.panda-logo .face {
  position: absolute;
  inset: 0;
  background: #fff;
  border-radius: 50%;
  border: 3px solid #000;
  display: grid;
  place-items: center;
}
.panda-logo .eye {
  position: absolute;
  width: 12px;
  height: 16px;
  background: #000;
  border-radius: 50% 50% 40% 40%;
  top: 16px;
}
.panda-logo .eye.left { left: 12px; transform: rotate(-12deg); }
.panda-logo .eye.right { right: 12px; transform: rotate(12deg); }
.panda-logo .nose {
  position: absolute;
  bottom: 12px;
  width: 12px;
  height: 8px;
  background: #000;
  border-radius: 8px 8px 12px 12px;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

/* Hero */
.hero {
  padding: 80px 24px 40px;
  display: grid;
  gap: 28px;
  max-width: 1100px;
  margin: 0 auto;
}

.hero-content {
  display: grid;
  gap: 18px;
}

.headline {
  margin: 0;
  font-size: clamp(26px, 4.6vw, 52px);
  line-height: 1.06;
  letter-spacing: -0.5px;
  background: conic-gradient(from 220deg, var(--text), var(--accent), var(--accent-2), var(--text));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 0 18px color-mix(in oklab, var(--accent), transparent 80%);
}

.subhead {
  margin: 0;
  font-size: clamp(14px, 2.2vw, 18px);
  color: var(--muted);
  max-width: 850px;
}

.cta-row {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 18px;
  border-radius: 999px;
  text-decoration: none;
  font-weight: 700;
  border: 1px solid transparent;
  transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease;
  will-change: transform;
}

.btn.primary {
  background: linear-gradient(90deg, var(--accent), var(--accent-2));
  color: #fff;
  box-shadow: 0 10px 30px color-mix(in oklab, var(--accent), transparent 65%);
}
.btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 14px 34px color-mix(in oklab, var(--accent-2), transparent 55%);
}

.btn.ghost {
  background: transparent;
  color: var(--text);
  border-color: color-mix(in oklab, var(--text), transparent 80%);
}
.btn.ghost:hover {
  transform: translateY(-2px);
  background: color-mix(in oklab, var(--card), transparent 15%);
}

/* Hero badges */
.hero-badges {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.badge {
  border-radius: 999px;
  padding: 8px 12px;
  font-weight: 700;
  font-size: 12px;
  color: var(--text);
  background:
    radial-gradient(120px 60px at 10% 10%, color-mix(in oklab, var(--neon), transparent 55%), transparent 40%),
    linear-gradient(180deg, color-mix(in oklab, var(--card), transparent 0%), color-mix(in oklab, var(--card), transparent 10%));
  border: 1px solid color-mix(in oklab, var(--text), transparent 85%);
}

/* Panels */
.panel {
  padding: 40px 24px;
}
.panel.compact { padding: 24px; }

.panel .panel-content,
.panel > .section-title,
#skills .section-title {
  max-width: 1100px;
  margin: 0 auto;
}

.section-title {
  font-size: clamp(18px, 2.8vw, 28px);
  margin: 0 0 12px;
}

.lead {
  color: var(--muted);
  font-size: 16px;
  margin: 0 0 12px;
}

.about-list {
  margin: 0;
  padding: 0 0 0 18px;
  color: var(--text);
  display: grid;
  gap: 8px;
}

/* Cards */
.cards {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 18px;
}

.card {
  grid-column: span 12;
  background: linear-gradient(180deg, color-mix(in oklab, var(--card), transparent 0%), color-mix(in oklab, var(--card), transparent 8%));
  border: 1px solid color-mix(in oklab, var(--text), transparent 88%);
  border-radius: var(--radius);
  overflow: clip;
  box-shadow: var(--shadow-md);
  transform-style: preserve-3d;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  will-change: transform;
}
@media (min-width: 700px) {
  .card { grid-column: span 6; }
}
@media (min-width: 1000px) {
  .card { grid-column: span 4; }
}

.card-top {
  position: relative;
  height: 140px;
  background:
    radial-gradient(300px 140px at -10% 120%, color-mix(in oklab, var(--accent), transparent 70%), transparent 60%),
    radial-gradient(260px 120px at 120% -20%, color-mix(in oklab, var(--accent-2), transparent 72%), transparent 60%),
    linear-gradient(180deg, color-mix(in oklab, var(--surface), transparent 0%), color-mix(in oklab, var(--surface), transparent 10%));
  border-bottom: 1px solid color-mix(in oklab, var(--text), transparent 90%);
}

.react-orb {
  position: absolute;
  inset: 0;
  margin: auto;
  width: 84px;
  height: 84px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #93f9ff, #4da1ff 60%, #2b46ff 100%);
  box-shadow:
    0 0 40px rgba(77, 161, 255, 0.55),
    inset 0 10px 30px rgba(255,255,255,0.25);
  animation: orbPulse 4s ease-in-out infinite;
}
.react-orb.teal {
  background: radial-gradient(circle at 30% 30%, #93ffe1, #21c3a3 60%, #029b7a 100%);
  box-shadow: 0 0 40px rgba(33, 195, 163, 0.55), inset 0 10px 30px rgba(255,255,255,0.25);
}
.react-orb.violet {
  background: radial-gradient(circle at 30% 30%, #e9a6ff, #a96bff 60%, #6b2bff 100%);
  box-shadow: 0 0 40px rgba(169, 107, 255, 0.55), inset 0 10px 30px rgba(255,255,255,0.25);
}

@keyframes orbPulse {
  0%, 100% { transform: translateZ(40px) scale(0.98); filter: saturate(0.9); }
  50% { transform: translateZ(60px) scale(1.02); filter: saturate(1.1); }
}

.card-body {
  padding: 14px 14px 16px;
  display: grid;
  gap: 10px;
}
.card-body h4 {
  margin: 0;
  font-size: 18px;
}
.card-body p {
  margin: 0;
  color: var(--muted);
  min-height: 34px;
}

.chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
.chips span {
  font-size: 11px;
  padding: 6px 10px;
  border-radius: 999px;
  background: var(--pill-bg);
  border: 1px solid color-mix(in oklab, var(--text), transparent 86%);
}

.actions {
  display: flex;
  gap: 12px;
}
.text-link {
  color: var(--neon);
  text-decoration: none;
  font-weight: 700;
}
.text-link:hover { text-decoration: underline; }

/* Skills marquee */
.marquee {
  overflow: clip;
  border-radius: var(--radius);
  border: 1px solid color-mix(in oklab, var(--text), transparent 88%);
  background: linear-gradient(180deg, color-mix(in oklab, var(--surface), transparent 0%), color-mix(in oklab, var(--surface), transparent 8%));
  box-shadow: var(--shadow-md);
}

.track {
  display: inline-flex;
  gap: 10px;
  padding: 12px;
  min-width: max-content;
  animation: scrollX 20s linear infinite;
}
@keyframes scrollX {
  to { transform: translateX(-50%); }
}

.pill {
  background: var(--pill-bg);
  border: 1px solid color-mix(in oklab, var(--text), transparent 85%);
  color: var(--text);
  font-weight: 800;
  padding: 8px 12px;
  border-radius: 999px;
  font-size: 12px;
}

/* Contact */
.contact-cta {
  display: flex;
  align-items: center;
  gap: 14px;
  flex-wrap: wrap;
}

.btn.goo {
  background: linear-gradient(90deg, var(--accent), var(--accent-2));
  color: #fff;
  border: none;
  box-shadow:
    0 10px 30px color-mix(in oklab, var(--accent), transparent 60%),
    inset 0 -8px 20px color-mix(in oklab, #000, transparent 80%);
  overflow: hidden;
}
.btn.goo .shine {
  position: absolute;
  inset: 0;
  background: radial-gradient(120px 40px at -10% -20%, rgba(255,255,255,0.8), transparent 50%),
              radial-gradient(120px 40px at 120% 120%, rgba(255,255,255,0.4), transparent 50%);
  mix-blend-mode: screen;
  transform: translateX(-20%);
  animation: shine 3.4s ease-in-out infinite;
}
@keyframes shine {
  0% { transform: translateX(-30%); opacity: 0.9; }
  50% { transform: translateX(0%); opacity: 0.6; }
  100% { transform: translateX(-30%); opacity: 0.9; }
}

.socials {
  display: inline-flex;
  gap: 8px;
}
.social {
  display: inline-grid;
  place-items: center;
  width: 36px;
  height: 36px;
  border-radius: 10px;
  text-decoration: none;
  color: var(--text);
  background: color-mix(in oklab, var(--card), transparent 10%);
  border: 1px solid color-mix(in oklab, var(--text), transparent 88%);
}
.social:hover { background: color-mix(in oklab, var(--card), transparent 0%); }

/* Footer */
.site-footer {
  text-align: center;
  padding: 28px 16px 60px;
  color: var(--muted);
}

/* Scroll reveal base */
.reveal {
  opacity: 0;
  transform: translateY(16px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}
.reveal.visible {
  opacity: 1;
  transform: translateY(0px);
}
