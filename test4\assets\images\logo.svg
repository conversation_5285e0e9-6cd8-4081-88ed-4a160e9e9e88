<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Panda.dev neon logo">
  <defs>
    <linearGradient id="lg" x1="0" x2="1" y1="0" y2="1">
      <stop offset="0%" stop-color="#3cffc0"/>
      <stop offset="100%" stop-color="#8a7dff"/>
    </linearGradient>
  </defs>
  <rect width="128" height="128" rx="22" fill="#0b0f13"/>
  <g transform="translate(64,64)">
    <circle r="40" fill="none" stroke="url(#lg)" stroke-width="10" opacity=".9"/>
    <circle r="40" fill="none" stroke="#ff6ad5" stroke-width="4" stroke-dasharray="12 10" opacity=".6"/>
    <g transform="translate(0,0)">
      <circle cx="-12" cy="-6" r="7" fill="#dfe7ee"/>
      <circle cx="12" cy="-6" r="7" fill="#dfe7ee"/>
      <circle cx="0" cy="10" r="6" fill="#dfe7ee"/>
    </g>
  </g>
</svg>