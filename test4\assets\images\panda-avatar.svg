<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Panda avatar wearing neon headphones">
  <defs>
    <radialGradient id="g" cx="50%" cy="35%" r="70%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity=".9"/>
      <stop offset="100%" stop-color="#dfe7ee" stop-opacity=".85"/>
    </radialGradient>
    <linearGradient id="glow" x1="0" x2="1" y1="0" y2="1">
      <stop offset="0%" stop-color="#3cffc0"/>
      <stop offset="100%" stop-color="#ff6ad5"/>
    </linearGradient>
  </defs>
  <rect width="512" height="512" rx="32" fill="#0b0f13"/>
  <g transform="translate(256,260)">
    <circle r="180" fill="url(#g)"/>
    <g fill="#0b0f13" opacity=".95">
      <ellipse cx="-80" cy="-40" rx="60" ry="72"/>
      <ellipse cx="80" cy="-40" rx="60" ry="72"/>
      <circle cx="-70" cy="-160" r="40"/>
      <circle cx="70" cy="-160" r="40"/>
    </g>
    <g>
      <circle cx="-50" cy="-30" r="26" fill="#0b0f13"/>
      <circle cx="50" cy="-30" r="26" fill="#0b0f13"/>
      <circle cx="0" cy="30" r="26" fill="#0b0f13"/>
    </g>
    <g>
      <path d="M-120 -110 a140 140 0 0 1 240 0" fill="none" stroke="url(#glow)" stroke-width="22" stroke-linecap="round" opacity=".8"/>
      <circle cx="-150" cy="-90" r="26" fill="#121720" stroke="url(#glow)" stroke-width="6"/>
      <circle cx="150" cy="-90" r="26" fill="#121720" stroke="url(#glow)" stroke-width="6"/>
    </g>
  </g>
</svg>