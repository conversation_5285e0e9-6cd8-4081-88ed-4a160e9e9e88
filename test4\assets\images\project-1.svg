<?xml version="1.0" encoding="UTF-8"?>
<svg width="1280" height="720" viewBox="0 0 1280 720" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Bamboo Board project cover">
  <defs>
    <linearGradient id="bg1" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#0b0f13"/>
      <stop offset="100%" stop-color="#14202b"/>
    </linearGradient>
    <radialGradient id="glow1" cx="80%" cy="10%" r="60%">
      <stop offset="0%" stop-color="#3cffc0" stop-opacity=".35"/>
      <stop offset="100%" stop-color="transparent"/>
    </radialGradient>
    <radialGradient id="glow2" cx="10%" cy="70%" r="60%">
      <stop offset="0%" stop-color="#8a7dff" stop-opacity=".28"/>
      <stop offset="100%" stop-color="transparent"/>
    </radialGradient>
  </defs>
  <rect width="1280" height="720" fill="url(#bg1)"/>
  <rect width="1280" height="720" fill="url(#glow1)"/>
  <rect width="1280" height="720" fill="url(#glow2)"/>
  <g transform="translate(0,0)">
    <g fill="none" stroke="#3cffc0" opacity=".22">
      <rect x="160" y="160" width="220" height="320" rx="16"/>
      <rect x="420" y="160" width="220" height="320" rx="16"/>
      <rect x="680" y="160" width="220" height="320" rx="16"/>
      <rect x="940" y="160" width="180" height="320" rx="16"/>
    </g>
    <g fill="#3cffc0" opacity=".12">
      <circle cx="270" cy="220" r="6"/>
      <circle cx="530" cy="220" r="6"/>
      <circle cx="790" cy="220" r="6"/>
      <circle cx="1040" cy="220" r="6"/>
    </g>
  </g>
  <g transform="translate(640,580)" text-anchor="middle" font-family="Inter, Arial, sans-serif">
    <text x="0" y="0" font-size="46" fill="#dfe7ee">Bamboo Board</text>
    <text x="0" y="40" font-size="22" fill="#9fb1c3">React · TypeScript · Vite · Styled Components</text>
  </g>
</svg>