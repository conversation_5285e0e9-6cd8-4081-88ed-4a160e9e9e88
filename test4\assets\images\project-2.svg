<?xml version="1.0" encoding="UTF-8"?>
<svg width="1280" height="720" viewBox="0 0 1280 720" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Snack Tracker project cover">
  <defs>
    <linearGradient id="bg2" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#0b0f13"/>
      <stop offset="100%" stop-color="#1a1630"/>
    </linearGradient>
    <radialGradient id="g21" cx="85%" cy="15%" r="60%">
      <stop offset="0%" stop-color="#ff6ad5" stop-opacity=".3"/>
      <stop offset="100%" stop-color="transparent"/>
    </radialGradient>
    <radialGradient id="g22" cx="10%" cy="80%" r="60%">
      <stop offset="0%" stop-color="#3cffc0" stop-opacity=".28"/>
      <stop offset="100%" stop-color="transparent"/>
    </radialGradient>
  </defs>
  <rect width="1280" height="720" fill="url(#bg2)"/>
  <rect width="1280" height="720" fill="url(#g21)"/>
  <rect width="1280" height="720" fill="url(#g22)"/>

  <!-- Chart-like elements -->
  <g transform="translate(140,140)" fill="none" stroke="#8a7dff" opacity=".55" stroke-width="2">
    <polyline points="0,320 120,260 240,280 360,210 480,240 600,160 720,190 840,120" />
  </g>
  <g transform="translate(140,140)" fill="#3cffc0" opacity=".22">
    <rect x="40" y="200" width="40" height="120" rx="8"/>
    <rect x="160" y="170" width="40" height="150" rx="8"/>
    <rect x="280" y="220" width="40" height="100" rx="8"/>
    <rect x="400" y="140" width="40" height="180" rx="8"/>
    <rect x="520" y="110" width="40" height="210" rx="8"/>
    <rect x="640" y="190" width="40" height="130" rx="8"/>
  </g>

  <g transform="translate(640,580)" text-anchor="middle" font-family="Inter, Arial, sans-serif">
    <text x="0" y="0" font-size="46" fill="#dfe7ee">Snack Tracker</text>
    <text x="0" y="40" font-size="22" fill="#9fb1c3">React · Recharts · Jest · Cypress</text>
  </g>
</svg>