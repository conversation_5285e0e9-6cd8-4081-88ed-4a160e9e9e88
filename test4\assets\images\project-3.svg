<?xml version="1.0" encoding="UTF-8"?>
<svg width="1280" height="720" viewBox="0 0 1280 720" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Pawfolio Builder project cover">
  <defs>
    <linearGradient id="bg3" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#0b0f13"/>
      <stop offset="100%" stop-color="#121f2b"/>
    </linearGradient>
    <radialGradient id="g31" cx="85%" cy="15%" r="60%">
      <stop offset="0%" stop-color="#8a7dff" stop-opacity=".35"/>
      <stop offset="100%" stop-color="transparent"/>
    </radialGradient>
    <radialGradient id="g32" cx="10%" cy="80%" r="60%">
      <stop offset="0%" stop-color="#3cffc0" stop-opacity=".25"/>
      <stop offset="100%" stop-color="transparent"/>
    </radialGradient>
  </defs>
  <rect width="1280" height="720" fill="url(#bg3)"/>
  <rect width="1280" height="720" fill="url(#g31)"/>
  <rect width="1280" height="720" fill="url(#g32)"/>

  <!-- Card templates motif -->
  <g transform="translate(160,160)" fill="none" stroke="#3cffc0" stroke-width="2" opacity=".32">
    <rect x="0" y="0" width="260" height="160" rx="16"/>
    <rect x="300" y="0" width="260" height="160" rx="16"/>
    <rect x="600" y="0" width="260" height="160" rx="16"/>
    <rect x="150" y="200" width="260" height="160" rx="16"/>
    <rect x="450" y="200" width="260" height="160" rx="16"/>
  </g>

  <g transform="translate(640,580)" text-anchor="middle" font-family="Inter, Arial, sans-serif">
    <text x="0" y="0" font-size="46" fill="#dfe7ee">Pawfolio Builder</text>
    <text x="0" y="40" font-size="22" fill="#9fb1c3">React · Next.js · TypeScript · Playwright</text>
  </g>
</svg>