/* 
  Panda React Dev "Pawfolio"
  - Neon cyber-bamboo theme with glassmorphism
  - Responsive, accessible, animated
*/

/* Web fonts: fun display + clean sans-serif, with fallbacks */
@import url("https://fonts.googleapis.com/css2?family=Londrina+Solid:wght@300;400;700&family=Readex+Pro:wght@200..700&display=swap");

/* CSS Variables */
:root {
  --bg: #0b0f12;
  --bg-elev: #0f151a;
  --panel: rgba(255, 255, 255, 0.06);
  --panel-border: rgba(255, 255, 255, 0.12);
  --text: #e6f3f1;
  --muted: #a9c7c1;
  --primary: #2fffd8; /* neon teal */
  --primary-2: #0ef1a5;
  --accent: #ff57c3; /* neon pink */
  --accent-2: #c27bff; /* purple */
  --bamboo: #58d686; /* soft bamboo green */
  --warning: #ffdc74;
  --error: #ff7b8b;

  --radius: 16px;
  --radius-sm: 12px;
  --radius-lg: 22px;
  --space-1: .25rem;
  --space-2: .5rem;
  --space-3: .75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-7: 3rem;

  --shadow-1: 0 10px 30px rgba(0, 0, 0, .35);
  --shadow-neon: 0 0 24px rgba(47, 255, 216, .35), 0 0 40px rgba(199, 122, 255, .18);

  --speed-fast: 140ms;
  --speed: 220ms;
  --speed-slow: 420ms;
  --easing: cubic-bezier(.2,.9,.2,1);

  --font-display: "Londrina Solid", system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  --font-sans: "Readex Pro", ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;

  --link-underline: linear-gradient(currentColor, currentColor);
}

html[data-theme="light"] {
  --bg: #f6faf9;
  --bg-elev: #ffffff;
  --panel: rgba(0, 0, 0, 0.05);
  --panel-border: rgba(0, 0, 0, 0.08);
  --text: #0e2320;
  --muted: #375d56;
  --primary: #00c7a3;
  --primary-2: #00a88a;
  --accent: #e30092;
  --accent-2: #7a3cff;
  --bamboo: #1aa862;
  --shadow-1: 0 8px 24px rgba(0,0,0,.08);
  --shadow-neon: 0 0 0 rgba(0,0,0,0);
}

/* Respects reduced motion */
@media (prefers-reduced-motion: reduce) {
  * { animation: none !important; transition: none !important; scroll-behavior: auto !important; }
}

/* Base */
* { box-sizing: border-box; }
html, body { height: 100%; }
html { scroll-behavior: smooth; }
body {
  margin: 0;
  background: radial-gradient(1200px 800px at 70% -10%, rgba(199,122,255,.12), transparent 60%),
              radial-gradient(900px 900px at -10% 30%, rgba(47,255,216,.12), transparent 60%),
              var(--bg);
  color: var(--text);
  font-family: var(--font-sans);
  line-height: 1.6;
}

/* Utilities */
.visually-hidden {
  position: absolute !important;
  height: 1px; width: 1px;
  overflow: hidden;
  clip: rect(1px,1px,1px,1px);
  white-space: nowrap;
}

.container {
  width: min(1180px, 100% - 2rem);
  margin-inline: auto;
}

.grid { display: grid; gap: var(--space-6); }
.flex { display: flex; gap: var(--space-4); align-items: center; }
.glass {
  background: var(--panel);
  border: 1px solid var(--panel-border);
  backdrop-filter: blur(10px);
  border-radius: var(--radius);
  box-shadow: var(--shadow-1);
}
.shadow { box-shadow: var(--shadow-1); }
.gradient-text {
  background: linear-gradient(90deg, var(--primary), var(--accent-2));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
a { color: inherit; text-decoration: none; }
a.link, .site-footer nav a, .project-actions .link {
  background-image: var(--link-underline);
  background-size: 0 2px;
  background-position: 0 100%;
  background-repeat: no-repeat;
  transition: background-size var(--speed) var(--easing), color var(--speed) var(--easing);
}
a.link:hover, .site-footer nav a:hover, .project-actions .link:hover { background-size: 100% 2px; color: var(--primary); }

/* Focus styles */
:focus-visible {
  outline: 2px dashed var(--accent);
  outline-offset: 3px;
  border-radius: 6px;
}

/* Header */
.site-header {
  position: sticky; top: 0; z-index: 40;
  background: color-mix(in oklab, var(--bg) 90%, transparent);
  border-bottom: 1px solid var(--panel-border);
  backdrop-filter: blur(6px);
}
.header-inner {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  gap: var(--space-4);
  padding: .6rem 0;
}
.logo { display: inline-flex; align-items: center; gap: .6rem; font-family: var(--font-display); letter-spacing: .5px; }
.logo img { filter: drop-shadow(0 0 8px rgba(47,255,216,.35)); }
.logo-text { font-size: 1.3rem; }
.site-nav { justify-self: center; }
.site-nav ul { display: flex; gap: var(--space-5); margin: 0; padding: 0; list-style: none; }
.nav-link {
  position: relative; padding: .25rem .1rem;
}
.nav-link::after {
  content: ""; position: absolute; left: 0; right: 0; bottom: -2px; height: 2px; 
  background: linear-gradient(90deg, var(--primary), var(--accent-2));
  transform: scaleX(0); transform-origin: left; transition: transform var(--speed) var(--easing);
}
.nav-link:hover::after, .nav-link.active::after { transform: scaleX(1); }
.nav-toggle { display: none; }

.theme-toggle {
  width: 44px; height: 28px; border-radius: 999px;
  border: 1px solid var(--panel-border); background: var(--panel);
  position: relative;
  transition: background var(--speed);
}
.theme-toggle .toggle-icon {
  position: absolute; top: 2px; left: 2px;
  width: 22px; height: 22px; border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #fff 10%, var(--primary) 40%, var(--accent-2) 100%);
  box-shadow: var(--shadow-neon);
  transition: transform var(--speed) var(--easing);
}
html[data-theme="light"] .theme-toggle .toggle-icon { transform: translateX(16px); }

/* Sections */
.section { padding: var(--space-7) 0; }
.section-header { text-align: center; margin-bottom: var(--space-6); }
.section-title {
  font-family: var(--font-display);
  font-size: clamp(1.6rem, 1.2rem + 1.5vw, 2.3rem);
  margin: 0 0 .25rem;
}
.section-subtitle { color: var(--muted); margin: 0; }

/* Hero */
.hero-inner { display: grid; gap: var(--space-6); align-items: center; grid-template-columns: 1fr; }
.hero-media { position: relative; justify-self: center; }
.avatar {
  width: clamp(180px, 40vw, 280px); height: auto;
  filter: drop-shadow(0 20px 30px rgba(0,0,0,.35));
}
.react-orbits .orbit {
  position: absolute; border: 1px dashed rgba(47,255,216,.35);
  border-radius: 50%;
  animation: spin 14s linear infinite;
}
.orbit-1 { width: 110%; height: 110%; top: -5%; left: -5%; }
.orbit-2 { width: 140%; height: 140%; top: -20%; left: -20%; animation-duration: 18s; }
.orbit-3 { width: 170%; height: 170%; top: -35%; left: -35%; animation-duration: 24s; }

.headline {
  font-size: clamp(1.6rem, 1.1rem + 2.6vw, 3rem);
  line-height: 1.1;
  margin: 0 0 .75rem;
}
.headline-prefix { color: var(--muted); margin-right: .5ch; }
.typewriter { color: var(--primary); white-space: nowrap; }
.caret {
  display: inline-block; width: 2px; height: 1em; background: var(--primary);
  margin-left: 2px; transform: translateY(2px);
  animation: caret 1s step-end infinite;
}
.subtitle { color: var(--muted); margin-block: .5rem 1.25rem; }
.hero-ctas { display: flex; gap: var(--space-4); flex-wrap: wrap; }

.btn {
  display: inline-flex; align-items: center; justify-content: center;
  padding: .7rem 1rem; border-radius: var(--radius-sm);
  border: 1px solid var(--panel-border);
  transition: transform var(--speed) var(--easing), box-shadow var(--speed), background var(--speed);
  cursor: pointer; color: var(--text);
}
.btn-primary {
  background: linear-gradient(140deg, color-mix(in oklab, var(--primary) 60%, #fff 0%), var(--accent-2));
  box-shadow: var(--shadow-neon);
}
.btn-ghost { background: var(--panel); }
.btn:hover { transform: translateY(-2px); }
.btn:active { transform: translateY(0); }

/* About */
.about-grid {
  display: grid; gap: var(--space-6);
  grid-template-columns: 1fr; 
}
.about-card, .skills { padding: var(--space-6); }
.skill-chips { list-style: none; padding: 0; margin: var(--space-4) 0 0; display: grid; gap: .8rem; }
.skill-chips li { display: grid; grid-template-columns: 130px 1fr; align-items: center; gap: .75rem; }
.chip {
  display: inline-block; padding: .4rem .7rem; border-radius: 999px;
  font-weight: 600; letter-spacing: .2px; color: #04221c;
  background: linear-gradient(90deg, var(--primary), var(--bamboo));
}
.bar {
  --val: 60%;
  height: 10px; border-radius: 999px; overflow: hidden; background: rgba(255,255,255,.08);
}
.bar::before {
  content: ""; display: block; height: 100%;
  width: 0; border-radius: 999px;
  background: linear-gradient(90deg, var(--bamboo), var(--primary));
  box-shadow: inset 0 0 8px rgba(47,255,216,.45);
  transition: width 900ms var(--easing);
}
.revealed .bar::before { width: var(--val); }

/* Projects */
.project-grid { display: grid; grid-template-columns: 1fr; gap: var(--space-6); }
.project-card { overflow: hidden; }
.project-media {
  display: block; border: 0; padding: 0; background: transparent; width: 100%;
  aspect-ratio: 16/10; overflow: hidden; cursor: pointer; position: relative;
}
.project-media img {
  width: 100%; height: 100%; object-fit: cover; display: block;
  transition: transform 600ms var(--easing);
}
.project-card:hover .project-media img { transform: scale(1.04); }
.project-body { padding: var(--space-5); }
.project-body h3 { margin: 0 0 .3rem; font-family: var(--font-display); }
.project-body p { margin: 0 0 .6rem; color: var(--muted); }
.tags { list-style: none; display: flex; gap: .5rem; padding: 0; margin: .5rem 0 0; flex-wrap: wrap; }
.tags li {
  font-size: .85rem; padding: .25rem .5rem; border-radius: 999px; border: 1px solid var(--panel-border);
  background: color-mix(in oklab, var(--panel) 70%, transparent);
}
.project-actions { display: flex; gap: 1rem; margin-top: .8rem; }

/* Modal / Lightbox */
.modal { position: fixed; inset: 0; display: none; z-index: 80; }
.modal[aria-hidden="false"] { display: block; }
.modal-backdrop { position: absolute; inset: 0; background: rgba(0,0,0,.5); }
.modal-dialog {
  position: relative; z-index: 1; margin: 6vh auto; width: min(900px, calc(100% - 2rem)); padding: 0;
}
.modal-header, .modal-footer { padding: var(--space-4) var(--space-5); display: flex; align-items: center; justify-content: space-between; }
.modal-content { padding: var(--space-5); }
.modal-close {
  border: 1px solid var(--panel-border);
  background: var(--panel);
  border-radius: 8px; width: 36px; height: 36px; cursor: pointer; font-size: 20px; color: var(--text);
}

/* Carousel */
.carousel { position: relative; overflow: hidden; border-radius: var(--radius); }
.carousel-track { display: flex; transition: transform 420ms var(--easing); }
.carousel-track img { width: 100%; flex: 0 0 100%; object-fit: cover; aspect-ratio: 16/10; display: block; }
.carousel-btn {
  position: absolute; top: 50%; transform: translateY(-50%);
  background: var(--panel); color: var(--text); border: 1px solid var(--panel-border);
  border-radius: 999px; width: 38px; height: 38px; cursor: pointer;
}
.carousel-btn.prev { left: .5rem; } .carousel-btn.next { right: .5rem; }

/* Experience Timeline */
.timeline { list-style: none; margin: 0; padding: 0; display: grid; gap: var(--space-6); }
.timeline-item { position: relative; padding-left: 34px; }
.timeline-item::before {
  content: ""; position: absolute; left: 10px; top: 0; bottom: 0; width: 2px; background: linear-gradient(var(--bamboo), transparent 80%);
}
.timeline-dot {
  position: absolute; left: 2px; top: 8px;
  width: 16px; height: 16px; border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #fff, var(--primary));
  box-shadow: 0 0 0 3px color-mix(in oklab, var(--primary) 25%, transparent);
}
.timeline-card { padding: var(--space-5); }
.timeline-card .meta { color: var(--muted); margin: .25rem 0 .5rem; }

/* Fun facts */
.fun-grid { display: grid; gap: var(--space-6); grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); padding: 0; list-style: none; }
.fun-card { padding: var(--space-6); font-size: 1.05rem; }

/* Contact */
.contact-grid { display: grid; gap: var(--space-6); grid-template-columns: 1fr; }
#contact-form { padding: var(--space-6); }
.field { display: grid; gap: .4rem; margin-bottom: 1rem; }
label { font-weight: 600; }
input, textarea {
  width: 100%; padding: .75rem .9rem; border-radius: var(--radius-sm);
  border: 1px solid var(--panel-border); background: color-mix(in oklab, var(--panel) 80%, transparent);
  color: var(--text);
}
input:focus, textarea:focus { border-color: var(--primary); box-shadow: 0 0 0 3px color-mix(in oklab, var(--primary) 25%, transparent); }
.error { color: var(--error); font-size: .9rem; min-height: 1.2em; }
.form-status { min-height: 1.2em; margin-top: .5rem; color: var(--muted); }
.form-actions { margin-top: .5rem; }

.social { padding: var(--space-6); }
.social-links { list-style: none; padding: 0; margin: 0; display: grid; gap: .8rem; }
.icon-link { display: inline-flex; align-items: center; gap: .6rem; padding: .5rem .6rem; border-radius: 10px; transition: background var(--speed); }
.icon-link:hover { background: color-mix(in oklab, var(--panel) 70%, transparent); }
.icon-link img { width: 22px; height: 22px; filter: drop-shadow(0 0 10px rgba(47,255,216,.35)); }

/* Footer */
.site-footer { padding: var(--space-6) 0; border-top: 1px solid var(--panel-border); margin-top: var(--space-7); }
.footer-inner { display: flex; align-items: center; justify-content: space-between; gap: var(--space-4); flex-wrap: wrap; }
.site-footer nav { display: flex; gap: 1rem; }

/* Toast */
.toast {
  position: fixed; left: 50%; bottom: 30px; transform: translateX(-50%) translateY(120%);
  background: var(--panel); border: 1px solid var(--panel-border);
  padding: .8rem 1rem; border-radius: 999px; box-shadow: var(--shadow-1);
  transition: transform var(--speed) var(--easing);
  z-index: 120;
}
.toast.show { transform: translateX(-50%) translateY(0); }

/* Reveal animations */
.reveal { opacity: 0; transform: translateY(16px) scale(.98); transition: opacity 600ms var(--easing), transform 600ms var(--easing); }
.revealed { opacity: 1 !important; transform: none !important; }

/* Background canvas overlays slight vignette */
#bamboo-canvas {
  position: fixed; inset: 0; z-index: -1;
}

/* Keyframes */
@keyframes caret { 0%, 100% { opacity: 0; } 50% { opacity: 1; } }
@keyframes spin { to { transform: rotate(1turn); } }

/* Responsive */
@media (min-width: 760px) {
  .hero-inner { grid-template-columns: 1fr 1.2fr; }
  .about-grid { grid-template-columns: 1.2fr .8fr; }
  .project-grid { grid-template-columns: repeat(2, 1fr); }
  .contact-grid { grid-template-columns: 1.1fr .9fr; }
}
@media (min-width: 1020px) {
  .project-grid { grid-template-columns: repeat(3, 1fr); }
}

/* Mobile nav */
@media (max-width: 760px) {
  .nav-toggle { display: inline-flex; width: 40px; height: 32px; align-items: center; justify-content: center; flex-direction: column; gap: 4px; background: var(--panel); border: 1px solid var(--panel-border); border-radius: 8px; }
  .nav-toggle span { width: 20px; height: 2px; background: var(--text); }
  .site-nav ul { position: absolute; top: 60px; left: 0; right: 0; background: var(--bg-elev); border-bottom: 1px solid var(--panel-border); display: none; padding: .75rem 1rem; }
  .site-nav ul.open { display: grid; gap: .6rem; }
}

/* Elegant underlines for footer/nav already handled via a.link rules */
