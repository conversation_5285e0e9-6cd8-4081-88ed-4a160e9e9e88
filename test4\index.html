<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Pawfolio | Panda React Developer</title>
  <meta name="description" content="Cyber-bamboo portfolio of a whimsical Panda React Developer. Projects, experience, and ways to connect." />
  <meta property="og:title" content="Pawfolio | Panda React Developer" />
  <meta property="og:description" content="Cyber-bamboo portfolio of a whimsical Panda React Developer. Projects, experience, and ways to connect." />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="assets/images/project-1.svg" />
  <link rel="icon" href="assets/images/logo.svg" type="image/svg+xml" />
  <link rel="preload" href="assets/fonts/ReadexPro-VariableFont_HEXP,wght.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="assets/fonts/LondrinaSolid-VariableFont_wght.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="stylesheet" href="css/styles.css" />
</head>
<body>
  <a class="visually-hidden" href="#main">Skip to content</a>

  <!-- Decorative background canvas -->
  <canvas id="bamboo-canvas" aria-hidden="true"></canvas>

  <header class="site-header">
    <div class="container header-inner">
      <a href="#hero" class="logo" aria-label="Home">
        <img src="assets/images/logo.svg" alt="Panda React Dev logo" width="40" height="40" />
        <span class="logo-text">Pawfolio</span>
      </a>

      <nav class="site-nav" aria-label="Primary">
        <button class="nav-toggle" aria-expanded="false" aria-controls="nav-list" aria-label="Toggle navigation">
          <span></span><span></span><span></span>
        </button>
        <ul id="nav-list">
          <li><a class="nav-link" href="#projects">Projects</a></li>
          <li><a class="nav-link" href="#about">About</a></li>
          <li><a class="nav-link" href="#experience">Experience</a></li>
          <li><a class="nav-link" href="#fun">Fun Facts</a></li>
          <li><a class="nav-link" href="#contact">Contact</a></li>
        </ul>
      </nav>

      <button id="theme-toggle" class="theme-toggle" aria-label="Toggle theme" aria-pressed="false">
        <span class="toggle-icon" aria-hidden="true"></span>
      </button>
    </div>
  </header>

  <main id="main">
    <!-- Hero -->
    <section id="hero" class="section hero">
      <div class="container hero-inner">
        <div class="hero-media">
          <img class="avatar" src="assets/images/panda-avatar.svg" alt="Smiling panda avatar wearing neon coder glasses" width="280" height="280" />
          <div class="react-orbits" aria-hidden="true">
            <div class="orbit orbit-1"></div>
            <div class="orbit orbit-2"></div>
            <div class="orbit orbit-3"></div>
          </div>
        </div>
        <div class="hero-copy">
          <h1 class="headline" aria-live="polite">
            <span class="headline-prefix">Hi, I’m</span>
            <span class="gradient-text">Pandy</span>
            <span class="typewriter" id="typewriter" data-roles='["React Developer","Frontend Engineer","UI Tinkerer"]'>&nbsp;</span>
            <span class="caret" aria-hidden="true"></span>
          </h1>
          <p class="subtitle">
            I craft delightful UI with bamboo-smooth performance and neon-fresh interactions.
          </p>
          <div class="hero-ctas">
            <a class="btn btn-primary" href="#projects">View Projects</a>
            <a class="btn btn-ghost" href="#contact">Contact</a>
          </div>
        </div>
      </div>
    </section>

    <!-- About -->
    <section id="about" class="section about">
      <div class="container">
        <header class="section-header reveal">
          <h2 class="section-title">About</h2>
          <p class="section-subtitle">Who’s behind the paws?</p>
        </header>

        <div class="about-grid">
          <div class="about-card glass reveal">
            <h3>Bamboo Bytes</h3>
            <p>
              I’m a panda who writes React by day and munches bamboo by night.
              I love TypeScript, elegant state machines, and CSS that sparkles (just a little).
            </p>
            <p>
              My philosophy: components should be as reusable as bamboo shoots—snappy, resilient, and full of life.
            </p>
          </div>

          <div class="skills glass reveal">
            <h3>Skills</h3>
            <ul class="skill-chips" role="list">
              <li><span class="chip">React</span><div class="bar" style="--val:92%"></div></li>
              <li><span class="chip">TypeScript</span><div class="bar" style="--val:88%"></div></li>
              <li><span class="chip">JavaScript</span><div class="bar" style="--val:95%"></div></li>
              <li><span class="chip">CSS</span><div class="bar" style="--val:90%"></div></li>
              <li><span class="chip">Node</span><div class="bar" style="--val:75%"></div></li>
              <li><span class="chip">Testing</span><div class="bar" style="--val:80%"></div></li>
            </ul>
            <p class="skill-note">Animated bamboo bars grow on reveal.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Projects -->
    <section id="projects" class="section projects">
      <div class="container">
        <header class="section-header reveal">
          <h2 class="section-title">Featured Projects</h2>
          <p class="section-subtitle">A selection of paw-picked builds</p>
        </header>

        <div class="project-grid">
          <article class="project-card glass reveal">
            <button class="project-media" data-modal="modal-1" aria-label="Open details for Bamboo Board">
              <img loading="lazy" src="assets/images/project-1.svg" alt="Screenshot placeholder of Bamboo Board app" width="720" height="480" />
            </button>
            <div class="project-body">
              <h3>Bamboo Board</h3>
              <p>Kanban-style task garden cultivated with React, Vite and Zustand. Drag, drop, and grow.</p>
              <ul class="tags" role="list">
                <li>React</li><li>Vite</li><li>TypeScript</li><li>Zustand</li>
              </ul>
              <div class="project-actions">
                <a href="#" class="link" aria-label="View repo for Bamboo Board">Repo</a>
                <a href="#" class="link" aria-label="Open live demo of Bamboo Board">Live</a>
              </div>
            </div>
          </article>

          <article class="project-card glass reveal">
            <button class="project-media" data-modal="modal-2" aria-label="Open details for Snack Tracker">
              <img loading="lazy" src="assets/images/project-2.svg" alt="Screenshot placeholder of Snack Tracker app" width="720" height="480" />
            </button>
            <div class="project-body">
              <h3>Snack Tracker</h3>
              <p>Macronutrient-aware snack logger. Hooks, context, and charts keep your bamboo balanced.</p>
              <ul class="tags" role="list">
                <li>React</li><li>Styled Components</li><li>Jest</li><li>Cypress</li>
              </ul>
              <div class="project-actions">
                <a href="#" class="link" aria-label="View repo for Snack Tracker">Repo</a>
                <a href="#" class="link" aria-label="Open live demo of Snack Tracker">Live</a>
              </div>
            </div>
          </article>

          <article class="project-card glass reveal">
            <button class="project-media" data-modal="modal-3" aria-label="Open details for Pawfolio Builder">
              <img loading="lazy" src="assets/images/project-3.svg" alt="Screenshot placeholder of Pawfolio Builder app" width="720" height="480" />
            </button>
            <div class="project-body">
              <h3>Pawfolio Builder</h3>
              <p>Component-driven site generator that composes layouts like stacking bamboo sticks.</p>
              <ul class="tags" role="list">
                <li>React</li><li>TypeScript</li><li>CSS Modules</li><li>Vitest</li>
              </ul>
              <div class="project-actions">
                <a href="#" class="link" aria-label="View repo for Pawfolio Builder">Repo</a>
                <a href="#" class="link" aria-label="Open live demo of Pawfolio Builder">Live</a>
              </div>
            </div>
          </article>
        </div>
      </div>

      <!-- Project Modals -->
      <div id="modal-1" class="modal" aria-hidden="true" role="dialog" aria-modal="true" aria-labelledby="modal-1-title">
        <div class="modal-backdrop" data-close></div>
        <div class="modal-dialog glass" role="document">
          <header class="modal-header">
            <h3 id="modal-1-title">Bamboo Board</h3>
            <button class="modal-close" aria-label="Close modal" data-close>&times;</button>
          </header>
          <div class="modal-content">
            <div class="carousel" data-carousel>
              <button class="carousel-btn prev" aria-label="Previous image" data-prev>&larr;</button>
              <div class="carousel-track">
                <img src="assets/images/project-1.svg" alt="Bamboo Board board view" />
                <img src="assets/images/project-2.svg" alt="Bamboo Board detail view" />
                <img src="assets/images/project-3.svg" alt="Bamboo Board stats view" />
              </div>
              <button class="carousel-btn next" aria-label="Next image" data-next>&rarr;</button>
            </div>
            <p>Kanban task garden built with React + Vite + Zustand. Features drag-and-drop columns, offline cache, and keyboard navigation.</p>
          </div>
          <footer class="modal-footer">
            <a href="#" class="btn btn-primary">Repo</a>
            <a href="#" class="btn btn-ghost">Live</a>
          </footer>
        </div>
      </div>

      <div id="modal-2" class="modal" aria-hidden="true" role="dialog" aria-modal="true" aria-labelledby="modal-2-title">
        <div class="modal-backdrop" data-close></div>
        <div class="modal-dialog glass" role="document">
          <header class="modal-header">
            <h3 id="modal-2-title">Snack Tracker</h3>
            <button class="modal-close" aria-label="Close modal" data-close>&times;</button>
          </header>
          <div class="modal-content">
            <div class="carousel" data-carousel>
              <button class="carousel-btn prev" aria-label="Previous image" data-prev>&larr;</button>
              <div class="carousel-track">
                <img src="assets/images/project-2.svg" alt="Snack Tracker daily log" />
                <img src="assets/images/project-3.svg" alt="Snack Tracker charts" />
                <img src="assets/images/project-1.svg" alt="Snack Tracker foods" />
              </div>
              <button class="carousel-btn next" aria-label="Next image" data-next>&rarr;</button>
            </div>
            <p>Track snacks with macros, interactive charts, and delightful micro-interactions. Tested with Jest & Cypress.</p>
          </div>
          <footer class="modal-footer">
            <a href="#" class="btn btn-primary">Repo</a>
            <a href="#" class="btn btn-ghost">Live</a>
          </footer>
        </div>
      </div>

      <div id="modal-3" class="modal" aria-hidden="true" role="dialog" aria-modal="true" aria-labelledby="modal-3-title">
        <div class="modal-backdrop" data-close></div>
        <div class="modal-dialog glass" role="document">
          <header class="modal-header">
            <h3 id="modal-3-title">Pawfolio Builder</h3>
            <button class="modal-close" aria-label="Close modal" data-close>&times;</button>
          </header>
          <div class="modal-content">
            <div class="carousel" data-carousel>
              <button class="carousel-btn prev" aria-label="Previous image" data-prev>&larr;</button>
              <div class="carousel-track">
                <img src="assets/images/project-3.svg" alt="Pawfolio Builder templates" />
                <img src="assets/images/project-1.svg" alt="Pawfolio Builder editor" />
                <img src="assets/images/project-2.svg" alt="Pawfolio Builder exports" />
              </div>
              <button class="carousel-btn next" aria-label="Next image" data-next>&rarr;</button>
            </div>
            <p>Composable React templates that snap together like bamboo segments. Type-safe props and themeable tokens.</p>
          </div>
          <footer class="modal-footer">
            <a href="#" class="btn btn-primary">Repo</a>
            <a href="#" class="btn btn-ghost">Live</a>
          </footer>
        </div>
      </div>
    </section>

    <!-- Experience -->
    <section id="experience" class="section experience">
      <div class="container">
        <header class="section-header reveal">
          <h2 class="section-title">Experience</h2>
          <p class="section-subtitle">A stroll through the bamboo grove</p>
        </header>

        <ol class="timeline">
          <li class="timeline-item reveal">
            <div class="timeline-dot"></div>
            <div class="timeline-card glass">
              <h3>React Bambooist • Bamboo Labs</h3>
              <p class="meta">2023 — Present</p>
              <p>Design systems gardener. Planted hooks, pruned effects, harvested blazing UI.</p>
            </div>
          </li>
          <li class="timeline-item reveal">
            <div class="timeline-dot"></div>
            <div class="timeline-card glass">
              <h3>Frontend Panda • Zen Forest</h3>
              <p class="meta">2021 — 2023</p>
              <p>Led UI modernization: dark mode, accessibility, and smooth scroll through dense undergrowth.</p>
            </div>
          </li>
          <li class="timeline-item reveal">
            <div class="timeline-dot"></div>
            <div class="timeline-card glass">
              <h3>Junior Pawgrammer • Snack Shack</h3>
              <p class="meta">2020 — 2021</p>
              <p>Automated snack restocking with a React dashboard; prevented bamboo shortages.</p>
            </div>
          </li>
        </ol>
      </div>
    </section>

    <!-- Fun -->
    <section id="fun" class="section fun">
      <div class="container">
        <header class="section-header reveal">
          <h2 class="section-title">Fun Facts</h2>
          <p class="section-subtitle">Playful stats from the grove</p>
        </header>
        <ul class="fun-grid" role="list">
          <li class="fun-card glass reveal" aria-label="Favourite Snack">
            🥢 <strong>15</strong> bamboo shoots/day
          </li>
          <li class="fun-card glass reveal" aria-label="Most-used Command">
            🧪 <strong>npm run test</strong> (loves green checks)
          </li>
          <li class="fun-card glass reveal" aria-label="Preferred Theme">
            🌗 <strong>Dark</strong> (neon pops!)
          </li>
          <li class="fun-card glass reveal" aria-label="Editor">
            💻 <strong>VS Code</strong> with panda-themed icon pack
          </li>
        </ul>
      </div>
    </section>

    <!-- Contact -->
    <section id="contact" class="section contact">
      <div class="container">
        <header class="section-header reveal">
          <h2 class="section-title">Contact</h2>
          <p class="section-subtitle">Let’s build something bamboo-tiful</p>
        </header>

        <div class="contact-grid">
          <form id="contact-form" class="glass reveal" novalidate aria-describedby="form-status">
            <div class="field">
              <label for="name">Name</label>
              <input id="name" name="name" type="text" required autocomplete="name" />
              <span class="error" id="error-name" role="alert"></span>
            </div>
            <div class="field">
              <label for="email">Email</label>
              <input id="email" name="email" type="email" required autocomplete="email" />
              <span class="error" id="error-email" role="alert"></span>
            </div>
            <div class="field">
              <label for="message">Message</label>
              <textarea id="message" name="message" rows="5" required></textarea>
              <span class="error" id="error-message" role="alert"></span>
            </div>
            <div class="form-actions">
              <button class="btn btn-primary" type="submit">Send Message</button>
            </div>
            <p id="form-status" class="form-status" aria-live="polite"></p>
          </form>

          <aside class="social glass reveal">
            <h3>Find me</h3>
            <ul class="social-links" role="list">
              <li>
                <a class="icon-link" href="#" aria-label="GitHub">
                  <img src="assets/icons/github.svg" alt="" role="presentation" />
                  <span>GitHub</span>
                </a>
              </li>
              <li>
                <a class="icon-link" href="#" aria-label="LinkedIn">
                  <img src="assets/icons/linkedin.svg" alt="" role="presentation" />
                  <span>LinkedIn</span>
                </a>
              </li>
              <li>
                <a class="icon-link" href="#" aria-label="Twitter">
                  <img src="assets/icons/twitter.svg" alt="" role="presentation" />
                  <span>Twitter</span>
                </a>
              </li>
              <li>
                <a class="icon-link" href="mailto:<EMAIL>" aria-label="Email">
                  <img src="assets/icons/mail.svg" alt="" role="presentation" />
                  <span>Email</span>
                </a>
              </li>
            </ul>
          </aside>
        </div>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container footer-inner">
      <p>&copy; <span id="year"></span> Panda. Made with ❤️ and bamboo.</p>
      <nav aria-label="Footer">
        <a href="#hero">Top</a>
        <a href="#projects">Projects</a>
        <a href="#contact">Contact</a>
      </nav>
    </div>
  </footer>

  <!-- Toast -->
  <div id="toast" class="toast" role="status" aria-live="polite" aria-atomic="true"></div>

  <script src="js/app.js"></script>
</body>
</html>
