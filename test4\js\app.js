/* Panda React Dev Pawfolio - app.js
   - Theme toggle with persistence
   - Typewriter rotating roles
   - IntersectionObserver reveal animations
   - Modal/lightbox with keyboard support + focus trap
   - Carousel controls
   - Smooth scroll + active nav highlight
   - Background floating bamboo particles
   - Contact form validation + toast
*/

(function () {
  const root = document.documentElement;
  const prefersLight = window.matchMedia && window.matchMedia("(prefers-color-scheme: light)").matches;
  const savedTheme = localStorage.getItem("theme");
  const initialTheme = savedTheme || (prefersLight ? "light" : "dark");
  root.setAttribute("data-theme", initialTheme);

  const themeToggle = document.getElementById("theme-toggle");
  if (themeToggle) {
    const setPressed = () => {
      themeToggle.setAttribute("aria-pressed", root.getAttribute("data-theme") === "light" ? "true" : "false");
    };
    setPressed();
    themeToggle.addEventListener("click", () => {
      const now = root.getAttribute("data-theme") === "dark" ? "light" : "dark";
      root.setAttribute("data-theme", now);
      localStorage.setItem("theme", now);
      setPressed();
    });
  }

  // Mobile nav toggle
  const navToggle = document.querySelector(".nav-toggle");
  const navList = document.getElementById("nav-list");
  if (navToggle && navList) {
    navToggle.addEventListener("click", () => {
      const expanded = navToggle.getAttribute("aria-expanded") === "true";
      navToggle.setAttribute("aria-expanded", String(!expanded));
      navList.classList.toggle("open", !expanded);
    });
    navList.addEventListener("click", (e) => {
      if (e.target.closest("a")) {
        navList.classList.remove("open");
        navToggle.setAttribute("aria-expanded", "false");
      }
    });
  }

  // Typewriter rotating roles
  const typeEl = document.getElementById("typewriter");
  if (typeEl) {
    const roles = JSON.parse(typeEl.getAttribute("data-roles") || "[]");
    let roleIdx = 0;
    let charIdx = 0;
    let deleting = false;
    const speed = { type: 70, delete: 45, pauseEnd: 1100, pauseStart: 400 };

    function tick() {
      const current = roles[roleIdx] || "";
      if (!deleting) {
        charIdx++;
        typeEl.textContent = current.slice(0, charIdx);
        if (charIdx === current.length) {
          deleting = true;
          setTimeout(tick, speed.pauseEnd);
          return;
        }
      } else {
        charIdx--;
        typeEl.textContent = current.slice(0, charIdx);
        if (charIdx === 0) {
          deleting = false;
          roleIdx = (roleIdx + 1) % roles.length;
          setTimeout(tick, speed.pauseStart);
          return;
        }
      }
      setTimeout(tick, deleting ? speed.delete : speed.type);
    }
    tick();
  }

  // IntersectionObserver reveal
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("revealed");
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.15 }
  );
  document.querySelectorAll(".reveal").forEach((el) => observer.observe(el));

  // Smooth scroll and active nav highlight
  const links = document.querySelectorAll('a[href^="#"]:not([href="#"])');
  links.forEach((a) =>
    a.addEventListener("click", (e) => {
      const id = a.getAttribute("href").slice(1);
      const target = document.getElementById(id);
      if (target) {
        e.preventDefault();
        target.scrollIntoView({ behavior: "smooth", block: "start" });
        history.replaceState(null, "", `#${id}`);
      }
    })
  );

  const sections = Array.from(document.querySelectorAll("main section[id]"));
  const navLinks = Array.from(document.querySelectorAll(".site-nav .nav-link"));
  const highlightOnScroll = () => {
    const y = window.scrollY + 120;
    let activeId = sections[0]?.id;
    for (const s of sections) {
      if (s.offsetTop <= y) activeId = s.id;
    }
    navLinks.forEach((lnk) => lnk.classList.toggle("active", lnk.getAttribute("href") === `#${activeId}`));
  };
  window.addEventListener("scroll", highlightOnScroll, { passive: true });
  highlightOnScroll();

  // Year in footer
  const yearEl = document.getElementById("year");
  if (yearEl) yearEl.textContent = String(new Date().getFullYear());

  // Modal / lightbox with focus trap + carousel
  const body = document.body;
  const openButtons = document.querySelectorAll("[data-modal]");
  const modals = new Map();
  document.querySelectorAll(".modal").forEach((m) => modals.set(m.id, m));

  let lastFocused = null;
  function trapFocus(modal) {
    const focusables = modal.querySelectorAll(
      'a[href], button:not([disabled]), textarea, input, select, [tabindex]:not([tabindex="-1"])'
    );
    if (!focusables.length) return;
    const first = focusables[0];
    const last = focusables[focusables.length - 1];
    function loop(e) {
      if (e.key !== "Tab") return;
      if (e.shiftKey && document.activeElement === first) {
        e.preventDefault();
        last.focus();
      } else if (!e.shiftKey && document.activeElement === last) {
        e.preventDefault();
        first.focus();
      }
    }
    modal.addEventListener("keydown", loop);
    return () => modal.removeEventListener("keydown", loop);
  }

  function openModal(id) {
    const modal = modals.get(id);
    if (!modal) return;
    lastFocused = document.activeElement;
    modal.setAttribute("aria-hidden", "false");
    body.style.overflow = "hidden";
    const cleanup = trapFocus(modal);
    modal._cleanupFocus = cleanup;
    const close = () => closeModal(modal);
    modal.querySelectorAll("[data-close]").forEach((btn) => btn.addEventListener("click", close, { once: true }));
    const backdrop = modal.querySelector(".modal-backdrop");
    if (backdrop) backdrop.addEventListener("click", close, { once: true });
    const esc = (e) => { if (e.key === "Escape") { close(); } };
    modal._esc = esc;
    document.addEventListener("keydown", esc);
    // focus first focusable
    const firstFocusable = modal.querySelector('button, [href], input, textarea, [tabindex]:not([tabindex="-1"])');
    if (firstFocusable) firstFocusable.focus();
    // init carousel transform to first slide
    initCarousel(modal);
  }

  function closeModal(modal) {
    modal.setAttribute("aria-hidden", "true");
    body.style.overflow = "";
    if (modal._cleanupFocus) modal._cleanupFocus();
    if (modal._esc) document.removeEventListener("keydown", modal._esc);
    if (lastFocused) lastFocused.focus();
  }

  openButtons.forEach((btn) =>
    btn.addEventListener("click", () => {
      const id = btn.getAttribute("data-modal");
      openModal(id);
    })
  );

  // Carousel
  function initCarousel(scope) {
    scope.querySelectorAll("[data-carousel]").forEach((carousel) => {
      const track = carousel.querySelector(".carousel-track");
      const slides = Array.from(track.children);
      let idx = 0;
      const set = () => {
        track.style.transform = `translateX(${idx * -100}%)`;
      };
      const prev = carousel.querySelector("[data-prev]");
      const next = carousel.querySelector("[data-next]");
      prev?.addEventListener("click", () => { idx = (idx - 1 + slides.length) % slides.length; set(); });
      next?.addEventListener("click", () => { idx = (idx + 1) % slides.length; set(); });
      // keyboard left/right inside modal content
      const onKey = (e) => {
        if (e.key === "ArrowLeft") { idx = (idx - 1 + slides.length) % slides.length; set(); }
        if (e.key === "ArrowRight") { idx = (idx + 1) % slides.length; set(); }
      };
      carousel.addEventListener("keydown", onKey);
      set();
    });
  }

  // Background particles (bamboo leaves as simple rotated rounded rects)
  const canvas = document.getElementById("bamboo-canvas");
  if (canvas) {
    const ctx = canvas.getContext("2d");
    const DPR = Math.min(window.devicePixelRatio || 1, 2);
    let w = 0, h = 0;
    const leaves = [];
    const COUNT = 60;
    const reduceMotion = window.matchMedia && window.matchMedia("(prefers-reduced-motion: reduce)").matches;

    function resize() {
      w = canvas.clientWidth = window.innerWidth;
      h = canvas.clientHeight = window.innerHeight;
      canvas.width = w * DPR; canvas.height = h * DPR;
      ctx.setTransform(DPR, 0, 0, DPR, 0, 0);
    }
    window.addEventListener("resize", resize);
    resize();

    function init() {
      leaves.length = 0;
      for (let i = 0; i < COUNT; i++) {
        leaves.push({
          x: Math.random() * w,
          y: Math.random() * h,
          s: Math.random() * 1.2 + 0.4,
          r: Math.random() * Math.PI * 2,
          v: Math.random() * 0.3 + 0.05,
          drift: (Math.random() - 0.5) * 0.3,
          hue: Math.random() * 10 + 150 // greenish
        });
      }
    }
    init();

    function draw() {
      ctx.clearRect(0, 0, w, h);
      for (const leaf of leaves) {
        ctx.save();
        ctx.translate(leaf.x, leaf.y);
        ctx.rotate(leaf.r);
        const grad = ctx.createLinearGradient(-10, 0, 10, 0);
        grad.addColorStop(0, `hsla(${leaf.hue}, 60%, 45%, .06)`);
        grad.addColorStop(1, `hsla(${leaf.hue + 30}, 70%, 55%, .16)`);
        ctx.fillStyle = grad;
        roundRect(ctx, -14 * leaf.s, -3 * leaf.s, 28 * leaf.s, 6 * leaf.s, 3 * leaf.s);
        ctx.fill();
        ctx.restore();

        if (!reduceMotion) {
          leaf.y += leaf.v + leaf.s * 0.1;
          leaf.x += Math.sin(leaf.y * 0.01) * 0.3 + leaf.drift;
          leaf.r += 0.002 * (leaf.drift > 0 ? 1 : -1);
          if (leaf.y > h + 20) { leaf.y = -20; leaf.x = Math.random() * w; }
          if (leaf.x > w + 20) { leaf.x = -20; }
          if (leaf.x < -20) { leaf.x = w + 20; }
        }
      }
      requestAnimationFrame(draw);
    }
    requestAnimationFrame(draw);

    function roundRect(ctx, x, y, w, h, r) {
      const rr = Math.min(r, h / 2, w / 2);
      ctx.beginPath();
      ctx.moveTo(x + rr, y);
      ctx.arcTo(x + w, y, x + w, y + h, rr);
      ctx.arcTo(x + w, y + h, x, y + h, rr);
      ctx.arcTo(x, y + h, x, y, rr);
      ctx.arcTo(x, y, x + w, y, rr);
      ctx.closePath();
    }
  }

  // Contact form validation + toast
  const form = document.getElementById("contact-form");
  const toast = document.getElementById("toast");
  function showToast(msg) {
    if (!toast) return;
    toast.textContent = msg;
    toast.classList.add("show");
    setTimeout(() => toast.classList.remove("show"), 2600);
  }

  if (form) {
    form.addEventListener("submit", (e) => {
      e.preventDefault();
      const name = form.querySelector("#name");
      const email = form.querySelector("#email");
      const message = form.querySelector("#message");
      const errName = form.querySelector("#error-name");
      const errEmail = form.querySelector("#error-email");
      const errMessage = form.querySelector("#error-message");
      let ok = true;

      // Simple validations
      if (!name.value.trim()) { errName.textContent = "Please enter your name."; ok = false; } else { errName.textContent = ""; }
      if (!email.validity.valid) { errEmail.textContent = "Please enter a valid email."; ok = false; } else { errEmail.textContent = ""; }
      if (message.value.trim().length < 8) { errMessage.textContent = "Please enter at least 8 characters."; ok = false; } else { errMessage.textContent = ""; }

      if (!ok) return;

      // Simulate submission latency
      const status = form.querySelector("#form-status");
      status.textContent = "Sending...";
      setTimeout(() => {
        status.textContent = "Message sent successfully. I’ll get back to you from the grove!";
        showToast("Message sent 🍃");
        form.reset();
      }, 900);
    });
  }
})();
