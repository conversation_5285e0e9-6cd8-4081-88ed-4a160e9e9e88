<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Panda • React Developer</title>
  <meta name="description" content="Portfo<PERSON> of Panda, a React developer who ships bamboo-fast UIs." />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Zen+Dots&family=Inter:wght@300;400;600;800&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="./styles.css" />
</head>
<body>
  <canvas id="bamboo-canvas" aria-hidden="true"></canvas>

  <header class="nav">
    <div class="brand">
      <span class="logo" aria-hidden="true">🐼</span>
      <span class="brand-text">Panda.dev</span>
    </div>
    <nav class="links">
      <a href="#about">About</a>
      <a href="#work">Work</a>
      <a href="#skills">Skills</a>
      <a href="#contact" class="pill">Contact</a>
    </nav>
  </header>

  <main>
    <section class="hero" id="home" data-parallax>
      <div class="hero-glow"></div>
      <div class="hero-content">
        <div class="hero-left">
          <h1>
            Hi, I'm <span class="accent">Panda</span><br />
            React Developer
          </h1>
          <p class="tagline">I grow component forests and ship bamboo-fast UIs</p>

          <div class="cta-row">
            <a class="btn primary" href="#work">View Work</a>
            <a class="btn ghost" href="#contact">Hire Me</a>
          </div>

          <div class="meta">
            <span class="chip">React</span>
            <span class="chip">TypeScript</span>
            <span class="chip">Next.js</span>
            <span class="chip">Tailwind</span>
          </div>
        </div>

        <div class="hero-right">
          <div class="panda-card floaty" aria-label="Illustration of a cool developer panda">
            <div class="panda-head">
              <div class="ear left"></div>
              <div class="ear right"></div>
              <div class="face">
                <div class="eye left">
                  <div class="pupil"></div>
                </div>
                <div class="eye right">
                  <div class="pupil"></div>
                </div>
                <div class="nose"></div>
                <div class="mouth"></div>
              </div>
            </div>
            <div class="panda-body">
              <div class="laptop">
                <span class="react-logo" aria-hidden="true"></span>
              </div>
              <div class="paw left"></div>
              <div class="paw right"></div>
            </div>
          </div>
          <div class="orbit">
            <span class="chip neon">Hooks</span>
            <span class="chip neon">Vite</span>
            <span class="chip neon">Redux</span>
            <span class="chip neon">Zustand</span>
          </div>
        </div>
      </div>
      <div class="scroll-indicator" aria-hidden="true">
        <span>Scroll</span>
        <div class="mouse">
          <div class="wheel"></div>
        </div>
      </div>
    </section>

    <section class="about section" id="about" data-parallax data-depth="0.02">
      <h2>About</h2>
      <p>
        I'm a full-stack panda specializing in React ecosystems. I design reusable component systems,
        craft accessible interfaces, and optimize performance until it purrs. When I'm not coding,
        I'm refactoring bamboo.
      </p>
      <div class="stats">
        <div class="stat">
          <span class="num" data-count="120">0</span>
          <span class="label">Components Shipped</span>
        </div>
        <div class="stat">
          <span class="num" data-count="98">0</span>
          <span class="label">Lighthouse Score</span>
        </div>
        <div class="stat">
          <span class="num" data-count="14">0</span>
          <span class="label">Design Systems</span>
        </div>
      </div>
    </section>

    <section class="work section" id="work" data-parallax data-depth="0.04">
      <h2>Selected Work</h2>
      <div class="grid">
        <article class="card tilt">
          <div class="card-glow"></div>
          <div class="card-header">
            <span class="pill">Next.js</span>
            <span class="pill outline">Edge</span>
          </div>
          <h3>Bamboo Commerce</h3>
          <p>Headless storefront with ISR, streaming SSR, and micro-interactions.</p>
          <ul class="tags">
            <li>Next 14</li><li>Stripe</li><li>Framer Motion</li>
          </ul>
          <a class="btn tiny" href="#" tabindex="0">Live Demo</a>
        </article>

        <article class="card tilt">
          <div class="card-glow"></div>
          <div class="card-header">
            <span class="pill">React</span>
            <span class="pill outline">PWA</span>
          </div>
          <h3>Forest Kanban</h3>
          <p>Offline-first Kanban with optimistic updates and data sync.</p>
          <ul class="tags">
            <li>React Query</li><li>Vite</li><li>Workbox</li>
          </ul>
          <a class="btn tiny" href="#" tabindex="0">Case Study</a>
        </article>

        <article class="card tilt">
          <div class="card-glow"></div>
          <div class="card-header">
            <span class="pill">Typescript</span>
            <span class="pill outline">DX</span>
          </div>
          <h3>Honeycomb UI</h3>
          <p>Composable component library with theming and tokens.</p>
          <ul class="tags">
            <li>TS</li><li>Storybook</li><li>Rollup</li>
          </ul>
          <a class="btn tiny" href="#" tabindex="0">Docs</a>
        </article>
      </div>
    </section>

    <section class="skills section" id="skills" data-parallax data-depth="0.03">
      <h2>Skills</h2>
      <div class="marquee" aria-hidden="true">
        <div class="marquee-track">
          <span>React</span><span>TypeScript</span><span>Next.js</span><span>GraphQL</span>
          <span>Vite</span><span>Redux</span><span>Zustand</span><span>Jest</span>
          <span>Testing Library</span><span>Storybook</span><span>Tailwind</span><span>Framer Motion</span>
        </div>
      </div>
      <div class="skill-cloud">
        <button class="skill-bubble" data-skill="React Hooks">Hooks</button>
        <button class="skill-bubble" data-skill="Suspense & Streaming">Suspense</button>
        <button class="skill-bubble" data-skill="RSC & Next.js App Router">RSC</button>
        <button class="skill-bubble" data-skill="Accessibility & Semantics">A11y</button>
        <button class="skill-bubble" data-skill="Performance & Core Web Vitals">Perf</button>
      </div>
    </section>

    <section class="contact section" id="contact" data-parallax data-depth="0.02">
      <h2>Contact</h2>
      <form class="contact-form" autocomplete="off" novalidate>
        <div class="row">
          <label>
            <span>Name</span>
            <input type="text" name="name" placeholder="Bamboo Enthusiast" required />
          </label>
          <label>
            <span>Email</span>
            <input type="email" name="email" placeholder="<EMAIL>" required />
          </label>
        </div>
        <label class="full">
          <span>Message</span>
          <textarea name="message" placeholder="Let's build something delightful!" rows="4" required></textarea>
        </label>
        <div class="row actions">
          <button class="btn primary" type="submit">Send Message</button>
          <span class="hint">No backend required — this triggers a playful animation.</span>
        </div>
      </form>
    </section>
  </main>

  <footer class="footer">
    <p>© <span id="year"></span> Panda • Built with React vibes and pure web tech.</p>
  </footer>

  <script src="./script.js"></script>
</body>
</html>