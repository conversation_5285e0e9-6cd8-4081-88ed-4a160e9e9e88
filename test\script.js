/* Panda Portfolio Interactions - Pure JS */

// Year in footer
document.getElementById('year').textContent = new Date().getFullYear();

// Simple parallax on sections with data-parallax
(function parallax() {
  const sections = [...document.querySelectorAll('[data-parallax]')];
  const clamp = (n, min, max) => Math.max(min, Math.min(max, n));

  function onScroll() {
    const vh = window.innerHeight;
    sections.forEach(sec => {
      const rect = sec.getBoundingClientRect();
      const depth = parseFloat(sec.dataset.depth || '0.03');
      const center = rect.top + rect.height / 2 - vh / 2;
      const translate = clamp(-center * depth, -60, 60);
      sec.style.transform = `translateY(${translate}px)`;
    });
  }

  onScroll();
  window.addEventListener('scroll', onScroll, { passive: true });
  window.addEventListener('resize', onScroll);
})();

// Canvas: animated bamboo stalks + fireflies
(function bambooCanvas() {
  const canvas = document.getElementById('bamboo-canvas');
  const ctx = canvas.getContext('2d', { alpha: true });
  let W = 0, H = 0, stalks = [], fireflies = [];

  function resize() {
    const dpr = Math.min(window.devicePixelRatio || 1, 2);
    W = canvas.width = Math.floor(window.innerWidth * dpr);
    H = canvas.height = Math.floor(window.innerHeight * dpr);
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
    initStalks();
    initFireflies();
  }

  function rand(a, b) { return a + Math.random() * (b - a); }

  function initStalks() {
    const count = Math.max(8, Math.floor(window.innerWidth / 180));
    stalks = Array.from({ length: count }).map((_, i) => {
      const x = (i + Math.random() * 0.6) * (window.innerWidth / count);
      return {
        x,
        baseY: H,
        height: rand(H * 0.35, H * 0.75),
        sway: rand(0.6, 1.6),
        phase: rand(0, Math.PI * 2),
        thickness: rand(2.5, 4.5),
        hue: rand(100, 125) // greenish
      };
    });
  }

  function initFireflies() {
    const count = Math.max(12, Math.floor(window.innerWidth / 120));
    fireflies = Array.from({ length: count }).map(() => ({
      x: rand(0, window.innerWidth),
      y: rand(0, window.innerHeight),
      vx: rand(-0.25, 0.25),
      vy: rand(-0.2, 0.2),
      size: rand(1.2, 2.2),
      alpha: rand(0.2, 0.7),
      tw: rand(0.01, 0.03),
      phase: rand(0, Math.PI * 2)
    }));
  }

  let t = 0;
  function draw() {
    t += 0.01;
    ctx.clearRect(0, 0, window.innerWidth, window.innerHeight);

    // mist
    const g = ctx.createLinearGradient(0, 0, 0, H);
    g.addColorStop(0, 'rgba(108,240,255,0.05)');
    g.addColorStop(1, 'rgba(166,255,108,0.04)');
    ctx.fillStyle = g;
    ctx.fillRect(0, 0, window.innerWidth, window.innerHeight);

    // bamboo stalks
    stalks.forEach((s, idx) => {
      const swayX = Math.sin(t * s.sway + s.phase) * 6;
      ctx.strokeStyle = `hsla(${s.hue}, 60%, 50%, ${0.25 + (idx / stalks.length) * 0.25})`;
      ctx.lineWidth = s.thickness;
      ctx.beginPath();
      ctx.moveTo(s.x + swayX * 0.2, s.baseY);
      ctx.quadraticCurveTo(
        s.x + swayX * 0.8,
        s.baseY - s.height * 0.5,
        s.x + swayX,
        s.baseY - s.height
      );
      ctx.stroke();

      // nodes
      ctx.lineWidth = s.thickness + 1;
      ctx.strokeStyle = `hsla(${s.hue}, 58%, 46%, 0.35)`;
      for (let y = s.baseY - 20; y > s.baseY - s.height; y -= 36) {
        ctx.beginPath();
        ctx.moveTo(s.x + swayX - 8, y);
        ctx.lineTo(s.x + swayX + 8, y);
        ctx.stroke();
      }
    });

    // fireflies
    fireflies.forEach(f => {
      f.x += f.vx + Math.sin(t + f.phase) * 0.2;
      f.y += f.vy + Math.cos(t * 0.8 + f.phase) * 0.15;

      if (f.x < -10) f.x = window.innerWidth + 10;
      if (f.x > window.innerWidth + 10) f.x = -10;
      if (f.y < -10) f.y = window.innerHeight + 10;
      if (f.y > window.innerHeight + 10) f.y = -10;

      const a = 0.2 + 0.6 * (0.5 + 0.5 * Math.sin(t * (2 * f.tw) + f.phase));
      ctx.fillStyle = `rgba(108,240,255,${a})`;
      ctx.beginPath();
      ctx.arc(f.x, f.y, f.size, 0, Math.PI * 2);
      ctx.fill();
    });

    requestAnimationFrame(draw);
  }

  resize();
  draw();
  window.addEventListener('resize', resize);
})();

// Tilt effect on .tilt cards
(function tiltCards() {
  const cards = document.querySelectorAll('.tilt');

  function handleMove(e) {
    const rect = this.getBoundingClientRect();
    const x = (e.clientX - rect.left) / rect.width;  // 0..1
    const y = (e.clientY - rect.top) / rect.height;  // 0..1
    const rotateY = (x - 0.5) * 10; // deg
    const rotateX = (0.5 - y) * 10;
    this.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-2px)`;
  }
  function reset() {
    this.style.transform = 'none';
  }

  cards.forEach(c => {
    c.addEventListener('mousemove', handleMove);
    c.addEventListener('mouseleave', reset);
  });
})();

// Count-up animation for stats
(function countUp() {
  const nums = document.querySelectorAll('.num[data-count]');
  let started = false;

  function run() {
    if (started) return;
    const anyVisible = [...nums].some(el => el.getBoundingClientRect().top < window.innerHeight - 60);
    if (!anyVisible) return;

    started = true;
    nums.forEach(el => {
      const target = parseInt(el.dataset.count, 10) || 0;
      const dur = 1200;
      const start = performance.now();
      function tick(now) {
        const p = Math.min(1, (now - start) / dur);
        const eased = 1 - Math.pow(1 - p, 3);
        el.textContent = Math.floor(eased * target);
        if (p < 1) requestAnimationFrame(tick);
        else el.textContent = String(target);
      }
      requestAnimationFrame(tick);
    });

    window.removeEventListener('scroll', run);
  }

  window.addEventListener('scroll', run, { passive: true });
  run();
})();

// Skill bubble ping + toast
(function skills() {
  const bubbles = document.querySelectorAll('.skill-bubble');

  const toast = document.createElement('div');
  toast.setAttribute('role', 'status');
  toast.style.position = 'fixed';
  toast.style.left = '50%';
  toast.style.bottom = '24px';
  toast.style.transform = 'translateX(-50%)';
  toast.style.padding = '10px 14px';
  toast.style.background = 'rgba(12,15,29,.9)';
  toast.style.border = '1px solid rgba(255,255,255,.12)';
  toast.style.borderRadius = '12px';
  toast.style.color = '#e7ecff';
  toast.style.fontSize = '13px';
  toast.style.boxShadow = '0 10px 30px rgba(0,0,0,.35)';
  toast.style.opacity = '0';
  toast.style.transition = 'opacity .2s, transform .2s';
  toast.style.pointerEvents = 'none';
  document.body.appendChild(toast);

  let hideTimer = 0;

  function showToast(text) {
    toast.textContent = text;
    toast.style.opacity = '1';
    toast.style.transform = 'translateX(-50%) translateY(-6px)';
    clearTimeout(hideTimer);
    hideTimer = setTimeout(() => {
      toast.style.opacity = '0';
      toast.style.transform = 'translateX(-50%) translateY(0)';
    }, 1600);
  }

  bubbles.forEach(b => {
    b.addEventListener('click', () => {
      const skill = b.getAttribute('data-skill') || 'Skill';
      b.animate(
        [
          { boxShadow: '0 0 0 0 rgba(108,240,255,.5)' },
          { boxShadow: '0 0 0 12px rgba(108,240,255,0)' }
        ],
        { duration: 600, easing: 'ease-out' }
      );
      showToast(`Panda loves ${skill}!`);
    });
  });
})();

// Contact form playful animation (no backend)
(function contactForm() {
  const form = document.querySelector('.contact-form');
  if (!form) return;

  form.addEventListener('submit', (e) => {
    e.preventDefault();
    const btn = form.querySelector('button[type="submit"]');
    const prev = btn.textContent;
    btn.disabled = true;
    btn.textContent = 'Sending...';

    // confetti-lite
    const confetti = document.createElement('div');
    confetti.style.position = 'fixed';
    confetti.style.inset = '0';
    confetti.style.pointerEvents = 'none';
    document.body.appendChild(confetti);

    const pieces = 80;
    for (let i = 0; i < pieces; i++) {
      const s = document.createElement('span');
      const size = 6 + Math.random() * 6;
      s.style.position = 'absolute';
      s.style.left = Math.random() * 100 + 'vw';
      s.style.top = '-5vh';
      s.style.width = size + 'px';
      s.style.height = size + 'px';
      s.style.background = i % 3 ? 'rgba(108,240,255,.9)' : 'rgba(166,255,108,.9)';
      s.style.filter = 'drop-shadow(0 0 6px rgba(108,240,255,.4))';
      s.style.transform = `rotate(${Math.random() * 360}deg)`;
      confetti.appendChild(s);

      const fall = s.animate(
        [
          { transform: s.style.transform + ' translateY(0)', opacity: 1 },
          { transform: s.style.transform + ` translateY(${100 + Math.random() * 60}vh)`, opacity: 0.8 }
        ],
        { duration: 1200 + Math.random() * 1200, easing: 'cubic-bezier(.2,.8,.2,1)' }
      );
      fall.onfinish = () => s.remove();
    }

    setTimeout(() => {
      btn.textContent = 'Sent!';
      btn.animate([{ transform: 'scale(1)' }, { transform: 'scale(1.06)' }, { transform: 'scale(1)' }], { duration: 300 });
      form.reset();
      setTimeout(() => {
        btn.disabled = false;
        btn.textContent = prev;
        confetti.remove();
      }, 900);
    }, 800);
  });
})();