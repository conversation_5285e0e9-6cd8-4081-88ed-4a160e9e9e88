:root{
  --bg:#070911;
  --card:#0c0f1d;
  --muted:#8a93b2;
  --text:#e7ecff;
  --accent:#6cf0ff;
  --accent-2:#a6ff6c;
  --neon:#00ffe1;
  --hot:#ff5bd7;
  --glow: 0 0 24px rgba(108,240,255,.35), 0 0 64px rgba(108,240,255,.2);
}

*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  font-family:Inter,system-ui,Segoe UI,Roboto,Helvetica,Arial,sans-serif;
  background: radial-gradient(1200px 600px at 80% -10%, rgba(0,255,224,.06), transparent 60%),
              radial-gradient(900px 600px at 0% -10%, rgba(166,255,108,.05), transparent 60%),
              var(--bg);
  color:var(--text);
  overflow-x:hidden;
}

/* Canvas background */
#bamboo-canvas{
  position:fixed; inset:0;
  width:100%; height:100%;
  z-index:-1;
  filter:contrast(105%) saturate(110%);
}

/* Navigation */
.nav{
  position:sticky; top:0; z-index:50;
  display:flex; align-items:center; justify-content:space-between;
  padding:16px 24px;
  background: linear-gradient(to bottom, rgba(7,9,17,0.85), rgba(7,9,17,0.55) 65%, transparent);
  backdrop-filter: blur(8px);
  border-bottom:1px solid rgba(255,255,255,.06);
}
.brand{display:flex; align-items:center; gap:10px; font-weight:800; letter-spacing:.3px}
.brand .logo{font-size:22px; filter: drop-shadow(0 0 8px rgba(255,255,255,.3))}
.brand-text{font-family:"Zen Dots",system-ui; font-size:14px; color:var(--muted)}
.links{display:flex; gap:16px; align-items:center}
.links a{
  color:var(--text); text-decoration:none; opacity:.8; font-size:14px;
  transition:opacity .2s, transform .2s;
}
.links a:hover{opacity:1; transform:translateY(-1px)}
.links .pill{
  border:1px solid rgba(255,255,255,.12);
  padding:8px 12px; border-radius:999px; background:rgba(255,255,255,.04)
}

/* Hero */
.hero{position:relative; padding:96px 24px 72px; overflow:clip}
.hero-content{
  max-width:1200px; margin:0 auto; display:grid; gap:32px;
  grid-template-columns: 1.1fr 0.9fr;
  align-items:center;
}
.hero-glow{
  position:absolute; inset:-20% -10% auto -10%; height:70%;
  background: radial-gradient(600px 300px at 20% 20%, rgba(108,240,255,.12), transparent 65%),
              radial-gradient(800px 300px at 80% 10%, rgba(166,255,108,.08), transparent 60%);
  pointer-events:none; filter: blur(24px);
}
.hero h1{
  font-size: clamp(32px, 6vw, 64px);
  line-height:1.05; margin:0 0 12px;
  text-shadow: 0 2px 24px rgba(0,0,0,.35);
}
.accent{
  color:var(--accent);
  text-shadow: 0 0 16px rgba(108,240,255,.45);
}
.tagline{color:var(--muted); margin:0 0 22px; font-size: clamp(14px, 2vw, 18px)}
.cta-row{display:flex; gap:12px; margin:18px 0 10px}
.btn{
  display:inline-flex; align-items:center; justify-content:center;
  gap:8px; padding:12px 16px; border-radius:12px; font-weight:600;
  text-decoration:none; color:var(--text); border:1px solid rgba(255,255,255,.14);
  background:rgba(255,255,255,.03); transition: transform .15s, box-shadow .15s, background .2s;
}
.btn.primary{
  background: linear-gradient(135deg, rgba(108,240,255,.22), rgba(166,255,108,.18));
  box-shadow: 0 8px 28px rgba(108,240,255,.15);
}
.btn.ghost:hover{background:rgba(255,255,255,.07)}
.btn.primary:hover{transform:translateY(-1px); box-shadow: 0 10px 34px rgba(108,240,255,.25)}
.btn.tiny{padding:8px 12px; font-size:13px; border-radius:10px}

.meta{display:flex; gap:8px; flex-wrap:wrap; margin-top:18px}
.chip{
  font-size:12px; letter-spacing:.2px; color:#cfe8ff;
  background:rgba(255,255,255,.05);
  border:1px solid rgba(255,255,255,.12);
  padding:6px 10px; border-radius:999px
}
.chip.neon{
  color:var(--accent); border-color:rgba(108,240,255,.4);
  box-shadow:0 0 0 1px rgba(108,240,255,.15), var(--glow);
}

/* Panda Illustration */
.panda-card{
  position:relative; width:min(420px, 90vw); aspect-ratio: 1 / 1;
  margin-inline:auto; background: linear-gradient(180deg, rgba(12,15,29,.9), rgba(12,15,29,.7));
  border:1px solid rgba(255,255,255,.08); border-radius:24px; padding:22px;
  box-shadow: inset 0 0 0 1px rgba(255,255,255,.04), 0 20px 60px rgba(0,0,0,.45);
}
.floaty{animation:floaty 6s ease-in-out infinite}
@keyframes floaty{0%,100%{transform:translateY(0)}50%{transform:translateY(-10px)}}

.panda-head{position:absolute; left:50%; transform:translateX(-50%); top:16%; width:62%}
.ear{position:absolute; width:28%; aspect-ratio:1/1; background:#0b0d14; border-radius:50%; top:-12%}
.ear.left{left:2%} .ear.right{right:2%}
.face{position:relative; width:100%; aspect-ratio:1/1; background:#fff; border-radius:46%;
  box-shadow: inset 0 -6px 0 0 #f0f0f0, 0 8px 20px rgba(0,0,0,.25)}
.eye{position:absolute; top:36%; width:26%; aspect-ratio: 1/1.2; background:#101522; border-radius:50%}
.eye.left{left:18%} .eye.right{right:18%}
.pupil{position:absolute; left:50%; top:50%; transform:translate(-50%,-50%); width:36%; aspect-ratio:1/1; background:#1cf2ff; border-radius:50%; box-shadow:0 0 16px rgba(28,242,255,.6)}
.nose{position:absolute; left:50%; top:58%; transform:translateX(-50%); width:12%; aspect-ratio:1/1; background:#0b0d14; border-radius:40%}
.mouth{position:absolute; left:50%; top:66%; transform:translateX(-50%); width:28%; height:10%; border-bottom:4px solid #0b0d14; border-radius:0 0 40% 40%}
.panda-body{position:absolute; left:50%; transform:translateX(-50%); bottom:8%; width:74%; height:40%; background:#0b0d14; border-radius:32px 32px 20px 20px; box-shadow:inset 0 8px 0 #161a29}
.paw{position:absolute; bottom:-4%; width:22%; height:26%; background:#0b0d14; border-radius:24px}
.paw.left{left:16%} .paw.right{right:16%}
.laptop{
  position:absolute; left:50%; top:4%; transform:translateX(-50%);
  width:78%; height:70%; background:#0e1322; border-radius:12px; border:1px solid rgba(255,255,255,.08);
  box-shadow: inset 0 0 0 1px rgba(255,255,255,.05), 0 10px 30px rgba(0,0,0,.4);
}
.react-logo{
  position:absolute; left:50%; top:50%; transform:translate(-50%,-50%) rotate(18deg);
  width:34%; aspect-ratio:1/1; border:3px solid var(--accent); border-radius:50%;
  box-shadow: var(--glow);
}
.react-logo::before,.react-logo::after{
  content:""; position:absolute; inset:-12% -45% -12% -45%;
  border:3px solid var(--accent); border-radius:50%; transform:rotate(60deg);
}
.react-logo::after{transform:rotate(-60deg)}

/* Orbit chips */
.orbit{
  position:absolute; right:-8px; top:16px; display:flex; flex-direction:column; gap:10px;
}
.orbit .chip{animation: orbitFloat 5s ease-in-out infinite}
.orbit .chip:nth-child(2){animation-delay:.2s}
.orbit .chip:nth-child(3){animation-delay:.4s}
.orbit .chip:nth-child(4){animation-delay:.6s}
@keyframes orbitFloat{0%,100%{transform:translateX(0)}50%{transform:translateX(-6px)}}

/* Scroll indicator */
.scroll-indicator{
  position:absolute; left:50%; bottom:12px; transform:translateX(-50%);
  display:flex; align-items:center; gap:10px; color:var(--muted); font-size:13px;
  opacity:.85;
}
.mouse{width:22px; height:34px; border:2px solid rgba(255,255,255,.35); border-radius:12px; position:relative}
.wheel{position:absolute; left:50%; top:6px; transform:translateX(-50%); width:3px; height:8px; background:rgba(255,255,255,.7); border-radius:2px; animation:wheel 1.6s ease-in-out infinite}
@keyframes wheel{0%{opacity:0; transform:translate(-50%,-3px)}30%{opacity:1}100%{opacity:0; transform:translate(-50%,6px)}}

/* Sections */
.section{padding:80px 24px}
.section h2{
  max-width:1200px; margin:0 auto 14px; font-size:28px; letter-spacing:.4px
}
.section p{max-width:1200px; margin:0 auto; color:#c6d2ff; opacity:.9}
.about .stats{
  max-width:1200px; margin:24px auto 0; display:grid; gap:14px;
  grid-template-columns: repeat(3, minmax(0,1fr));
}
.stat{background:linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02)); border:1px solid rgba(255,255,255,.08); border-radius:16px; padding:18px}
.num{font-size:28px; font-weight:800; color:var(--accent)}
.label{color:var(--muted); font-size:12px}

/* Work grid */
.work .grid{
  max-width:1200px; margin:18px auto 0;
  display:grid; grid-template-columns: repeat(3,minmax(0,1fr)); gap:18px;
}
.card{
  position:relative; background:linear-gradient(180deg, rgba(12,15,29,.9), rgba(12,15,29,.7));
  border:1px solid rgba(255,255,255,.08); border-radius:18px; padding:16px; overflow:hidden;
  transition:transform .2s ease, box-shadow .2s ease, border-color .2s ease;
}
.card:hover{transform:translateY(-4px); border-color:rgba(108,240,255,.35); box-shadow:0 14px 40px rgba(0,0,0,.35)}
.card-glow{position:absolute; inset:auto -30% 40% -30%; height:60%; background:radial-gradient(40% 60% at 50% 0%, rgba(108,240,255,.16), transparent 60%); filter:blur(24px); pointer-events:none}
.card-header{display:flex; gap:8px; margin-bottom:6px}
.pill{font-size:11px; padding:6px 8px; border-radius:999px; background:rgba(255,255,255,.06); border:1px solid rgba(255,255,255,.12); color:#d6e2ff}
.pill.outline{background:transparent}
.card h3{margin:4px 0 6px}
.card p{color:#cfe0ff; margin:0 0 10px; font-size:14px}
.tags{display:flex; flex-wrap:wrap; gap:8px; padding:0; margin:0 0 10px; list-style:none}
.tags li{font-size:11px; color:#cde8ff; opacity:.9}

/* Tilt effect hint */
.tilt{transform-style:preserve-3d; perspective:800px}
.tilt:hover .btn.tiny{transform:translateZ(20px)}

/* Skills */
.skills .marquee{
  overflow:hidden; border-top:1px solid rgba(255,255,255,.08); border-bottom:1px solid rgba(255,255,255,.08);
  background:rgba(255,255,255,.02); margin:10px 0 18px;
}
.marquee-track{
  display:flex; gap:30px; padding:10px 0; white-space:nowrap; animation:marquee 16s linear infinite;
  color:#bfe7ff;
}
@keyframes marquee{from{transform:translateX(0)}to{transform:translateX(-50%)}}

.skill-cloud{display:flex; flex-wrap:wrap; gap:12px; max-width:1200px; margin:0 auto}
.skill-bubble{
  border:1px solid rgba(108,240,255,.4); color:var(--accent); background:rgba(108,240,255,.08);
  padding:10px 14px; border-radius:999px; cursor:pointer;
  box-shadow: var(--glow); transition: transform .12s ease;
}
.skill-bubble:active{transform:scale(.98)}

/* Contact */
.contact .contact-form{max-width:900px; margin:0 auto}
.contact .row{display:grid; grid-template-columns:1fr 1fr; gap:12px}
.contact label{display:flex; flex-direction:column; gap:6px; font-size:13px; color:#bcd7ff}
.contact input,.contact textarea{
  width:100%; padding:12px 14px; border-radius:12px; border:1px solid rgba(255,255,255,.12);
  background: rgba(255,255,255,.04); color:var(--text); outline:none;
}
.contact input:focus,.contact textarea:focus{
  border-color: rgba(108,240,255,.6); box-shadow:0 0 0 3px rgba(108,240,255,.12)
}
.contact .full{grid-column:1/-1}
.contact .actions{align-items:center}
.hint{font-size:12px; color:var(--muted)}

/* Footer */
.footer{padding:36px 24px; text-align:center; color:var(--muted); border-top:1px solid rgba(255,255,255,.08); background:linear-gradient(0deg, rgba(255,255,255,.02), transparent)}

/* Responsive */
@media (max-width: 920px){
  .hero-content{grid-template-columns:1fr}
  .orbit{position:static; flex-direction:row; justify-content:center; margin-top:10px}
  .about .stats{grid-template-columns:1fr}
  .work .grid{grid-template-columns:1fr}
  .contact .row{grid-template-columns:1fr}
}

/* Focus styles */
:focus-visible{outline:2px solid var(--neon); outline-offset:2px}